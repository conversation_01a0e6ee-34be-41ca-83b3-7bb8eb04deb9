# 约球小程序云开发部署指南

## 🚀 部署前准备

### 环境要求
- 微信开发者工具 (最新版本)
- 微信小程序账号
- 云开发环境已开通

### 云开发环境信息
- **环境ID**: `cloud1-2g0h4d0h9d6b431f`
- **地域**: 上海
- **资源配额**: 根据实际需求配置

## 📦 部署步骤

### 1. 项目导入
1. 打开微信开发者工具
2. 选择"导入项目"
3. 选择项目根目录
4. 填入正确的AppID: `wx5c768a52e3a91e1d`

### 2. 云函数部署

#### 2.1 上传login云函数
```bash
# 在微信开发者工具中
1. 右键 cloudfunctions/login 目录
2. 选择 "上传并部署：云端安装依赖"
3. 等待部署完成
```

#### 2.2 上传getUserInfo云函数
```bash
# 在微信开发者工具中
1. 右键 cloudfunctions/getUserInfo 目录
2. 选择 "上传并部署：云端安装依赖"
3. 等待部署完成
```

#### 2.3 上传updateUserInfo云函数
```bash
# 在微信开发者工具中
1. 右键 cloudfunctions/updateUserInfo 目录
2. 选择 "上传并部署：云端安装依赖"
3. 等待部署完成
```

### 3. 数据库配置

#### 3.1 创建集合
在云开发控制台中创建以下集合：

```javascript
// users 集合 - 用户信息
{
  "_id": "自动生成",
  "openid": "用户openid",
  "unionid": "用户unionid",
  "nickName": "用户昵称",
  "avatarUrl": "头像URL",
  "phone": "手机号",
  "level": "运动水平",
  "createTime": "创建时间",
  "lastLoginTime": "最后登录时间",
  "loginCount": "登录次数",
  "joinDate": "加入日期"
}

// activities 集合 - 活动信息 (后续扩展)
{
  "_id": "自动生成",
  "organizerId": "组织者openid",
  "title": "活动标题",
  "description": "活动描述",
  "time": "活动时间",
  "location": "活动地点",
  "maxParticipants": "最大参与人数",
  "currentParticipants": "当前参与人数",
  "status": "活动状态",
  "createTime": "创建时间"
}

// activity_applications 集合 - 活动申请记录 (后续扩展)
{
  "_id": "自动生成",
  "activityId": "活动ID",
  "applicantId": "申请者openid",
  "status": "申请状态",
  "applyTime": "申请时间",
  "remark": "申请备注"
}

// activity_participants 集合 - 活动参与记录 (后续扩展)
{
  "_id": "自动生成",
  "activityId": "活动ID",
  "participantId": "参与者openid",
  "joinTime": "参与时间",
  "status": "参与状态"
}
```

#### 3.2 数据库权限配置
```javascript
// 在云开发控制台 -> 数据库 -> 安全规则中配置
{
  "read": true,
  "write": "auth.openid != null"
}
```

### 4. 小程序配置

#### 4.1 TDesign组件库安装
```bash
# 在项目根目录执行
npm install tdesign-miniprogram

# 在微信开发者工具中
工具 -> 构建npm
```

#### 4.2 云开发初始化检查
确认 `miniprogram/app.js` 中的云开发配置：
```javascript
wx.cloud.init({
  env: 'cloud1-2g0h4d0h9d6b431f',
  traceUser: true,
});
```

### 5. 权限配置

#### 5.1 小程序权限
在 `app.json` 中确认权限配置：
```json
{
  "permission": {
    "scope.userInfo": {
      "desc": "用于完善用户资料"
    },
    "scope.userLocation": {
      "desc": "用于获取您的位置信息，推荐附近活动"
    }
  }
}
```

#### 5.2 云函数权限
确保云函数有以下权限：
- 数据库读写权限
- 用户信息获取权限

## 🧪 测试验证

### 1. 功能测试清单

#### 用户登录功能
- [ ] 微信登录授权
- [ ] 新用户注册
- [ ] 老用户登录
- [ ] 用户信息获取
- [ ] 登录状态持久化

#### 用户信息管理
- [ ] 个人信息展示
- [ ] 昵称修改
- [ ] 手机号修改
- [ ] 运动水平修改
- [ ] 用户统计数据

#### 页面导航
- [ ] 底部Tab导航
- [ ] 页面跳转
- [ ] 返回功能

### 2. 测试步骤

#### 2.1 登录测试
1. 打开小程序
2. 进入"我的"页面
3. 点击"微信登录"
4. 授权用户信息
5. 验证登录成功

#### 2.2 信息编辑测试
1. 登录后点击"编辑"
2. 测试修改昵称
3. 测试修改手机号
4. 测试修改运动水平
5. 验证信息更新成功

#### 2.3 状态持久化测试
1. 登录成功后关闭小程序
2. 重新打开小程序
3. 验证登录状态保持

## 🔧 故障排除

### 常见问题

#### 1. 云函数调用失败
**问题**: 调用云函数时报错
**解决方案**:
- 检查云函数是否正确部署
- 确认环境ID是否正确
- 查看云函数日志排查错误

#### 2. 用户信息获取失败
**问题**: wx.getUserProfile调用失败
**解决方案**:
- 确认在真机上测试（开发者工具可能有限制）
- 检查用户是否拒绝授权
- 确认小程序权限配置

#### 3. 数据库操作失败
**问题**: 数据库读写失败
**解决方案**:
- 检查数据库权限配置
- 确认集合是否正确创建
- 查看云函数执行日志

#### 4. TDesign组件不显示
**问题**: 页面组件显示异常
**解决方案**:
- 确认npm包正确安装
- 执行"构建npm"操作
- 检查组件引用路径

### 调试技巧

#### 1. 云函数调试
```javascript
// 在云函数中添加日志
console.log('调试信息:', data);

// 查看云开发控制台 -> 云函数 -> 日志
```

#### 2. 前端调试
```javascript
// 在小程序中添加调试信息
console.log('用户信息:', userInfo);

// 使用微信开发者工具的调试面板
```

## 📊 性能优化

### 1. 云函数优化
- 合理使用云函数缓存
- 优化数据库查询语句
- 减少不必要的数据传输

### 2. 前端优化
- 图片懒加载
- 合理使用setData
- 避免频繁的网络请求

### 3. 数据库优化
- 创建合适的索引
- 优化查询条件
- 合理设计数据结构

## 🔄 版本更新

### 更新流程
1. 修改代码
2. 重新部署云函数（如有变更）
3. 上传小程序代码
4. 提交审核
5. 发布版本

### 注意事项
- 云函数更新需要重新部署
- 数据库结构变更需要兼容性处理
- 用户数据迁移需要谨慎操作

## ✅ 部署完成检查

部署完成后，请确认以下项目：

- [ ] 云函数正常部署并可调用
- [ ] 数据库集合正确创建
- [ ] 用户登录功能正常
- [ ] 信息编辑功能正常
- [ ] 页面导航正常
- [ ] 真机测试通过

完成以上检查后，约球小程序的用户认证功能就可以正常使用了！