<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>活动详情 - TDesign版本</title>
    <link href="https://cdn.jsdelivr.net/npm/tdesign-mobile-vue@1.0.0/dist/index.css" rel="stylesheet">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/tdesign-mobile-vue@1.0.0/dist/index.umd.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
        }
        
        .detail-container {
            height: 100vh;
            overflow-y: auto;
        }
        
        .hero-section {
            position: relative;
            height: 200px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 48px;
        }
        
        .hero-actions {
            position: absolute;
            top: 16px;
            left: 16px;
            right: 16px;
            display: flex;
            justify-content: space-between;
        }
        
        .content-section {
            background: white;
            margin: 12px;
            border-radius: 12px;
            overflow: hidden;
        }
        
        .organizer-info {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px;
        }
        
        .organizer-avatar {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }
        
        .info-grid {
            padding: 0 16px 16px;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .venue-image {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
            margin: 16px 0;
            border-radius: 8px;
        }
        
        .participants-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            padding: 16px;
        }
        
        .participant-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .participant-avatar {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }
        
        .floating-actions {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #333;
        }
        
        .activity-description {
            line-height: 1.6;
            color: #666;
            padding: 16px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="detail-container">
            <!-- 顶部图片区域 -->
            <div class="hero-section">
                <div class="hero-actions">
                    <t-button 
                        theme="default" 
                        variant="text" 
                        shape="circle" 
                        @click="goBack"
                        style="background: rgba(0,0,0,0.5); color: white;">
                        <t-icon name="chevron-left" size="20px"></t-icon>
                    </t-button>
                    <t-button 
                        theme="default" 
                        variant="text" 
                        shape="circle" 
                        @click="shareActivity"
                        style="background: rgba(0,0,0,0.5); color: white;">
                        <t-icon name="share" size="20px"></t-icon>
                    </t-button>
                </div>
                🏸
            </div>
            
            <!-- 活动基本信息 -->
            <div class="content-section">
                <div style="padding: 16px 16px 0;">
                    <h1 style="font-size: 20px; font-weight: bold; margin: 0 0 16px;">
                        {{ activity.title }}
                    </h1>
                </div>
                
                <!-- 主办人信息 -->
                <div class="organizer-info">
                    <div class="organizer-avatar">👨</div>
                    <div style="flex: 1;">
                        <div style="font-weight: 500;">{{ activity.organizer }}</div>
                        <div style="font-size: 12px; color: #999;">活动发起人</div>
                    </div>
                    <t-tag theme="success" variant="light" size="small">
                        {{ activity.status }}
                    </t-tag>
                </div>
                
                <!-- 活动详细信息 -->
                <div class="info-grid">
                    <div class="info-item">
                        <t-icon name="time" size="20px" color="#0052d9"></t-icon>
                        <div>
                            <div style="font-weight: 500;">{{ activity.time }}</div>
                            <div style="font-size: 12px; color: #999;">{{ activity.duration }}</div>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <t-icon name="location" size="20px" color="#0052d9"></t-icon>
                        <div>
                            <div style="font-weight: 500;">{{ activity.venue }}</div>
                            <div style="font-size: 12px; color: #999;">距离您 {{ activity.distance }}</div>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <t-icon name="user" size="20px" color="#0052d9"></t-icon>
                        <div>
                            <div style="font-weight: 500;">{{ activity.participants }}/{{ activity.maxParticipants }} 人</div>
                            <div style="font-size: 12px; color: #999;">还差 {{ activity.maxParticipants - activity.participants }} 人</div>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <t-icon name="money-circle" size="20px" color="#0052d9"></t-icon>
                        <div>
                            <div style="font-weight: 500;">¥{{ activity.price }} 人均</div>
                            <div style="font-size: 12px; color: #999;">场地费平摊</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 活动介绍 -->
            <div class="content-section">
                <div style="padding: 16px 16px 0;">
                    <div class="section-title">活动介绍</div>
                </div>
                <div class="activity-description">
                    {{ activity.description }}
                    <br><br>
                    <div style="display: flex; flex-wrap: wrap; gap: 8px; margin-top: 12px;">
                        <t-tag 
                            v-for="feature in activity.features" 
                            :key="feature" 
                            theme="primary" 
                            variant="light" 
                            size="small">
                            {{ feature }}
                        </t-tag>
                    </div>
                </div>
            </div>
            
            <!-- 场地照片 -->
            <div class="content-section">
                <div style="padding: 16px 16px 0;">
                    <div class="section-title">场地照片</div>
                </div>
                <div style="padding: 0 16px 16px;">
                    <div class="venue-image">
                        🏸 羽毛球场地
                    </div>
                </div>
            </div>
            
            <!-- 参与人员 -->
            <div class="content-section" style="margin-bottom: 100px;">
                <div style="padding: 16px 16px 0;">
                    <div class="section-title">参与人员 ({{ activity.participants }}人)</div>
                </div>
                <div class="participants-grid">
                    <div 
                        v-for="participant in participantsList" 
                        :key="participant.id" 
                        class="participant-item">
                        <div class="participant-avatar">{{ participant.avatar }}</div>
                        <div>
                            <div style="font-size: 14px; font-weight: 500;">{{ participant.name }}</div>
                            <div style="font-size: 12px; color: #999;">{{ participant.role }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 悬浮操作按钮 -->
        <div class="floating-actions">
            <t-fab 
                @click="joinActivity"
                style="background: #0052d9;">
                <t-icon name="user-add" size="24px"></t-icon>
            </t-fab>
            <t-fab 
                @click="bookVenue"
                style="background: #00a870;">
                <t-icon name="location" size="24px"></t-icon>
            </t-fab>
        </div>
        
        <!-- 分享弹窗 -->
        <t-action-sheet 
            v-model="shareVisible" 
            :items="shareOptions"
            @selected="handleShare">
        </t-action-sheet>
    </div>

    <script>
        const { createApp } = Vue;
        
        createApp({
            data() {
                return {
                    shareVisible: false,
                    activity: {
                        title: '周末羽毛球约战',
                        organizer: '张三',
                        status: '进行中',
                        time: '今天 19:00-21:00',
                        duration: '2小时',
                        venue: '浦东新区羽毛球馆',
                        distance: '2.3km',
                        participants: 6,
                        maxParticipants: 8,
                        price: 45,
                        description: '周末羽毛球约战，欢迎各位球友参加！场地设施完善，有专业的羽毛球场地。适合中等水平以上的球友，我们会根据水平进行分组对战。',
                        features: ['🏸 提供羽毛球拍租借', '🚿 场馆内有淋浴设施', '🅿️ 免费停车位充足', '💧 免费提供饮用水']
                    },
                    participantsList: [
                        { id: 1, name: '张三', role: '发起人', avatar: '👨' },
                        { id: 2, name: '李四', role: '已确认', avatar: '👩' },
                        { id: 3, name: '王五', role: '已确认', avatar: '👨' },
                        { id: 4, name: '赵六', role: '已确认', avatar: '👩' },
                        { id: 5, name: '钱七', role: '已确认', avatar: '👨' },
                        { id: 6, name: '孙八', role: '已确认', avatar: '👩' }
                    ],
                    shareOptions: [
                        { label: '微信好友', value: 'wechat' },
                        { label: '朋友圈', value: 'moments' },
                        { label: '复制链接', value: 'copy' }
                    ]
                }
            },
            methods: {
                goBack() {
                    this.$message.info('返回上一页');
                },
                shareActivity() {
                    this.shareVisible = true;
                },
                joinActivity() {
                    this.$dialog({
                        title: '申请参与活动',
                        content: '确定要申请参与这个活动吗？',
                        confirmBtn: '确定申请',
                        cancelBtn: '取消',
                        onConfirm: () => {
                            this.$message.success('申请已提交，等待发起人确认');
                        }
                    });
                },
                bookVenue() {
                    this.$message.info('跳转到预订场地页面');
                },
                handleShare(option) {
                    this.shareVisible = false;
                    this.$message.success(`已分享到${option.label}`);
                }
            }
        }).use(TDesignMobile).mount('#app');
    </script>
</body>
</html>