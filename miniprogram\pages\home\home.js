// pages/home/<USER>
Page({
  data: {
    currentCity: '上海',
    searchValue: '',
    showCitySelector: false,
    showFilter: false,
    loading: false,
    activities: [
      {
        id: 1,
        title: '周末羽毛球约战',
        time: '今天 19:00-21:00',
        location: '浦东新区羽毛球馆',
        price: 45,
        participants: 6,
        maxParticipants: 8,
        image: '🏸',
        tag: '🔥 热门',
        tagType: 'hot'
      },
      {
        id: 2,
        title: '篮球友谊赛',
        time: '明天 14:00-16:00',
        location: '徐汇区篮球场',
        price: 30,
        participants: 8,
        maxParticipants: 10,
        image: '🏀',
        tag: '🆕 新活动',
        tagType: 'new'
      },
      {
        id: 3,
        title: '网球双打体验',
        time: '周六 10:00-12:00',
        location: '静安区网球中心',
        price: 80,
        participants: 3,
        maxParticipants: 4,
        image: '🎾',
        tag: '⚡ 仅剩1位',
        tagType: 'limited'
      },
      {
        id: 4,
        title: '足球11人制',
        time: '周日 16:00-18:00',
        location: '浦西足球场',
        price: 25,
        participants: 18,
        maxParticipants: 22,
        image: '⚽',
        tag: '',
        tagType: ''
      },
      {
        id: 5,
        title: '乒乓球切磋',
        time: '今晚 20:00-22:00',
        location: '长宁区乒乓球馆',
        price: 35,
        participants: 4,
        maxParticipants: 6,
        image: '🏓',
        tag: '🔥 热门',
        tagType: 'hot'
      }
    ],
    cities: ['上海', '北京', '广州', '深圳', '杭州', '南京', '苏州', '成都'],
  },

  onLoad() {
    this.requestLocation();
    this.loadActivities();

    // 添加页面进入动画
    this.animatePageEnter();
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshActivities();
  },

  // 页面进入动画
  animatePageEnter() {
    // 延迟执行动画，确保页面已渲染
    setTimeout(() => {
      const query = this.createSelectorQuery();
      query.selectAll('.activity-card').boundingClientRect();
      query.exec((res) => {
        if (res[0]) {
          // 为每个活动卡片添加进入动画
          res[0].forEach((rect, index) => {
            setTimeout(() => {
              const selector = `.activity-card:nth-child(${index + 1})`;
              this.createSelectorQuery()
                .select(selector)
                .node()
                .exec((nodeRes) => {
                  if (nodeRes[0] && nodeRes[0].node) {
                    nodeRes[0].node.style.animation = `card-slide-up 0.6s ease forwards`;
                    nodeRes[0].node.style.animationDelay = `${index * 100}ms`;
                  }
                });
            }, index * 50);
          });
        }
      });
    }, 100);
  },

  // 刷新活动数据
  refreshActivities() {
    // 这里可以调用API刷新数据
    console.log('刷新活动数据');
  },

  // 请求用户位置
  requestLocation() {
    wx.getLocation({
      type: 'wgs84',
      success: (res) => {
        // 这里可以根据经纬度获取城市信息
        console.log('获取位置成功', res);
      },
      fail: () => {
        wx.showToast({
          title: '定位失败，默认显示上海',
          icon: 'none'
        });
      }
    });
  },

  // 显示城市选择器
  onCityTap() {
    this.setData({
      showCitySelector: true
    });
  },

  // 城市选择
  onCitySelect(e) {
    const city = e.currentTarget.dataset.city;
    this.setData({
      currentCity: city,
      showCitySelector: false
    });
    this.loadActivities();
  },

  // 关闭城市选择器
  onCloseCitySelector() {
    this.setData({
      showCitySelector: false
    });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击模态框内容时关闭模态框
  },

  // 获取运动主题样式类
  getSportTheme(sportIcon) {
    const sportThemeMap = {
      '🏸': 'sport-badminton',
      '🏀': 'sport-basketball',
      '🎾': 'sport-tennis',
      '⚽': 'sport-football',
      '🏓': 'sport-pingpong',
      '🏃': 'sport-running',
      '🏊': 'sport-swimming',
      '🚴': 'sport-cycling'
    };
    return sportThemeMap[sportIcon] || 'sport-badminton';
  },

  // 获取标签样式类
  getTagClass(tagType) {
    const tagClassMap = {
      'hot': 'sport-tag-hot',
      'new': 'sport-tag-new',
      'limited': 'sport-tag-limited'
    };
    return tagClassMap[tagType] || 'sport-tag';
  },

  // 立即参与活动
  onJoinActivity(e) {
    const activityId = e.currentTarget.dataset.id;
    console.log('参与活动:', activityId);

    // 添加触觉反馈
    wx.vibrateShort({
      type: 'light'
    });

    // 显示参与确认
    wx.showModal({
      title: '确认参与',
      content: '确定要参与这个活动吗？',
      confirmText: '立即参与',
      confirmColor: '#667eea',
      success: (res) => {
        if (res.confirm) {
          // 跳转到活动详情页
          wx.navigateTo({
            url: `/pages/activity-detail/activity-detail?id=${activityId}`
          });
        }
      }
    });
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchValue: e.detail.value
    });
  },

  // 搜索确认
  onSearchConfirm() {
    if (this.data.searchValue.trim()) {
      wx.showToast({
        title: `搜索: ${this.data.searchValue}`,
        icon: 'none'
      });
    }
  },

  // 显示筛选
  onFilterTap() {
    wx.showToast({
      title: '筛选功能开发中',
      icon: 'none'
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    wx.showNavigationBarLoading();
    
    setTimeout(() => {
      this.loadActivities();
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
    }, 1000);
  },

  // 上拉加载更多
  onReachBottom() {
    wx.showToast({
      title: '加载更多...',
      icon: 'loading',
      duration: 1000
    });
    
    // 模拟加载更多数据
    setTimeout(() => {
      wx.showToast({
        title: '已加载全部',
        icon: 'none'
      });
    }, 1000);
  },

  // 加载活动数据
  async loadActivities() {
    try {
      wx.showLoading({
        title: '加载中...'
      });
      
      // 调用云函数获取活动列表
      const result = await wx.cloud.callFunction({
        name: 'getActivities',
        data: {
          city: this.data.currentCity,
          page: 1,
          pageSize: 20
        }
      });
      
      if (result.result.success) {
        this.setData({
          activities: result.result.activities
        });
      } else {
        console.error('获取活动列表失败:', result.result.message);
        // 如果云函数失败，保持使用模拟数据
      }
      
    } catch (error) {
      console.error('加载活动数据失败:', error);
      // 保持使用模拟数据
    } finally {
      wx.hideLoading();
    }
  },

  // 点击活动卡片
  onActivityTap(e) {
    const activityId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/activity-detail/activity-detail?id=${activityId}`
    });
  },

  // 发布活动
  onPublishTap() {
    // 检查用户登录状态
    wx.checkSession({
      success: () => {
        // 已登录，跳转到发布页面
        wx.navigateTo({
          url: '/pages/publish-activity/publish-activity'
        });
      },
      fail: () => {
        // 未登录，提示用户登录
        wx.showModal({
          title: '需要登录',
          content: '发布活动需要先登录，是否前往登录？',
          success: (res) => {
            if (res.confirm) {
              wx.switchTab({
                url: '/pages/profile/profile'
              });
            }
          }
        });
      }
    });
  },

  // 页面显示时刷新数据
  onShow() {
    this.loadActivities();
  }
});