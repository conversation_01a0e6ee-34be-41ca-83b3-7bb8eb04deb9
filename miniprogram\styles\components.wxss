/**
 * 约球小程序 - 现代化组件库
 * Modern Component Library
 */

/* ========================================
   按钮组件系统
   ======================================== */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  padding: var(--space-md) var(--space-xl);
  border-radius: var(--radius-lg);
  font-size: var(--font-base);
  font-weight: var(--font-medium);
  line-height: 1;
  text-align: center;
  cursor: pointer;
  transition: all var(--duration-normal) var(--easing-ease);
  position: relative;
  overflow: hidden;
  border: none;
  outline: none;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--duration-slow) var(--easing-ease);
}

.btn:active::before {
  left: 100%;
}

/* 按钮尺寸 */
.btn-xs {
  padding: var(--space-xs) var(--space-md);
  font-size: var(--font-xs);
  border-radius: var(--radius-sm);
}

.btn-sm {
  padding: var(--space-sm) var(--space-lg);
  font-size: var(--font-sm);
  border-radius: var(--radius-md);
}

.btn-lg {
  padding: var(--space-lg) var(--space-2xl);
  font-size: var(--font-lg);
  border-radius: var(--radius-xl);
}

.btn-xl {
  padding: var(--space-xl) var(--space-3xl);
  font-size: var(--font-xl);
  border-radius: var(--radius-xl);
}

/* 按钮变体 */
.btn-primary {
  background: var(--primary-gradient);
  color: var(--text-inverse);
  box-shadow: var(--shadow-md);
}

.btn-primary:active {
  transform: translateY(2rpx);
  box-shadow: var(--shadow-sm);
}

.btn-secondary {
  background: var(--secondary-gradient);
  color: var(--text-inverse);
  box-shadow: var(--shadow-md);
}

.btn-success {
  background: var(--success-gradient);
  color: var(--text-inverse);
  box-shadow: var(--shadow-md);
}

.btn-outline {
  background: transparent;
  color: var(--color-primary);
  border: 2rpx solid var(--color-primary);
}

.btn-outline:active {
  background: var(--color-primary);
  color: var(--text-inverse);
}

.btn-ghost {
  background: rgba(102, 126, 234, 0.1);
  color: var(--color-primary);
}

.btn-ghost:active {
  background: rgba(102, 126, 234, 0.2);
}

/* 圆形按钮 */
.btn-circle {
  width: 96rpx;
  height: 96rpx;
  border-radius: var(--radius-full);
  padding: 0;
}

.btn-circle.btn-sm {
  width: 64rpx;
  height: 64rpx;
}

.btn-circle.btn-lg {
  width: 128rpx;
  height: 128rpx;
}

/* ========================================
   卡片组件系统
   ======================================== */

.card {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all var(--duration-normal) var(--easing-ease);
}

.card:active {
  transform: translateY(-4rpx);
  box-shadow: var(--shadow-lg);
}

.card-glass {
  background: var(--bg-glass);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.card-gradient {
  background: var(--primary-gradient);
  color: var(--text-inverse);
}

.card-header {
  padding: var(--space-lg);
  border-bottom: 1rpx solid var(--border-light);
}

.card-body {
  padding: var(--space-lg);
}

.card-footer {
  padding: var(--space-lg);
  border-top: 1rpx solid var(--border-light);
  background: var(--neutral-50);
}

/* 活动卡片特殊样式 */
.activity-card {
  background: var(--bg-secondary);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all var(--duration-normal) var(--easing-ease);
}

.activity-card:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-lg);
}

.activity-card-image {
  height: 320rpx;
  background: var(--primary-gradient);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.activity-card-content {
  padding: var(--space-xl);
}

/* ========================================
   表单组件系统
   ======================================== */

.form-group {
  margin-bottom: var(--space-lg);
}

.form-label {
  display: block;
  font-size: var(--font-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  margin-bottom: var(--space-sm);
}

.form-input {
  width: 100%;
  padding: var(--space-md) var(--space-lg);
  border: 2rpx solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--font-base);
  color: var(--text-primary);
  background: var(--bg-secondary);
  transition: all var(--duration-normal) var(--easing-ease);
}

.form-input:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.form-input-error {
  border-color: var(--color-danger);
}

.form-input-error:focus {
  box-shadow: 0 0 0 6rpx rgba(239, 68, 68, 0.1);
}

.form-textarea {
  min-height: 200rpx;
  resize: vertical;
}

/* ========================================
   标签组件系统
   ======================================== */

.tag {
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-xs) var(--space-md);
  border-radius: var(--radius-full);
  font-size: var(--font-xs);
  font-weight: var(--font-medium);
  line-height: 1;
}

.tag-primary {
  background: rgba(102, 126, 234, 0.1);
  color: var(--color-primary);
}

.tag-success {
  background: rgba(16, 185, 129, 0.1);
  color: var(--color-success);
}

.tag-warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--color-warning);
}

.tag-danger {
  background: rgba(239, 68, 68, 0.1);
  color: var(--color-danger);
}

.tag-hot {
  background: var(--danger-gradient);
  color: var(--text-inverse);
}

.tag-new {
  background: var(--success-gradient);
  color: var(--text-inverse);
}

.tag-limited {
  background: var(--warning-gradient);
  color: var(--text-inverse);
}

/* ========================================
   头像组件系统
   ======================================== */

.avatar {
  display: inline-block;
  border-radius: var(--radius-full);
  overflow: hidden;
  background: var(--neutral-200);
  position: relative;
}

.avatar-xs {
  width: 48rpx;
  height: 48rpx;
}

.avatar-sm {
  width: 64rpx;
  height: 64rpx;
}

.avatar-md {
  width: 96rpx;
  height: 96rpx;
}

.avatar-lg {
  width: 128rpx;
  height: 128rpx;
}

.avatar-xl {
  width: 160rpx;
  height: 160rpx;
}

.avatar-ring {
  border: 4rpx solid var(--color-primary);
}

.avatar-ring-gradient {
  border: 4rpx solid transparent;
  background: var(--primary-gradient);
  background-clip: padding-box;
}

/* ========================================
   徽章组件系统
   ======================================== */

.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 40rpx;
  height: 40rpx;
  padding: 0 var(--space-xs);
  border-radius: var(--radius-full);
  font-size: var(--font-xs);
  font-weight: var(--font-bold);
  line-height: 1;
  color: var(--text-inverse);
  background: var(--color-danger);
}

.badge-dot {
  width: 16rpx;
  height: 16rpx;
  min-width: 16rpx;
  padding: 0;
}

.badge-primary {
  background: var(--color-primary);
}

.badge-success {
  background: var(--color-success);
}

/* ========================================
   加载组件系统
   ======================================== */

.loading {
  display: inline-block;
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid var(--neutral-200);
  border-top: 4rpx solid var(--color-primary);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-lg {
  width: 64rpx;
  height: 64rpx;
  border-width: 6rpx;
}

/* ========================================
   分割线组件
   ======================================== */

.divider {
  height: 1rpx;
  background: var(--border-light);
  margin: var(--space-lg) 0;
}

.divider-thick {
  height: 2rpx;
  background: var(--border-medium);
}

.divider-gradient {
  height: 2rpx;
  background: var(--primary-gradient);
}

/* ========================================
   浮动操作按钮
   ======================================== */

.fab {
  position: fixed;
  bottom: 160rpx;
  right: var(--space-xl);
  width: 112rpx;
  height: 112rpx;
  border-radius: var(--radius-full);
  background: var(--primary-gradient);
  color: var(--text-inverse);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-xl);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-fixed);
  transition: all var(--duration-normal) var(--easing-bounce);
}

.fab:active {
  transform: scale(0.9);
  box-shadow: var(--shadow-md);
}

.fab-pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}
