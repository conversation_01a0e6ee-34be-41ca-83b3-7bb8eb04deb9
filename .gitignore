# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
miniprogram/miniprogram_npm/*


# 环境变量
.env
.env.* 

# 日志文件
logs/
*.log

# 操作系统文件
.DS_Store
Thumbs.db

# 前端构建输出
frontend/dist/
frontend/build/
frontend/.cache/

# 后端构建输出
backend/dist/
backend/build/
backend/.cache/

# 测试覆盖率
coverage/
*.lcov

# 编辑器配置
.vscode/
.idea/

# 依赖锁文件（如不需要上传）
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# 其他
*.swp
*.swo