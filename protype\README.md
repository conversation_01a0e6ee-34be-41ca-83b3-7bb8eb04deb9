# 约球小程序原型

基于PRC文档实现的约球小程序原型，使用HTML + Tailwind CSS + TDesign设计风格开发。

## 📱 设备规格

- **屏幕尺寸**: iPhone 15 Pro (393x852px)
- **设计风格**: TDesign
- **技术栈**: HTML + Tailwind CSS + JavaScript

## 🏗️ 项目结构

```
protype/
├── index.html              # 主入口文件（iPhone外观容器）
├── home.html              # 首页（活动列表）
├── activity-detail.html   # 活动详情页
├── booking.html           # 预订场地页
├── profile.html           # 我的页面
├── join-activity.html     # 申请参与活动页
├── venue-detail.html      # 场地详情页
└── README.md             # 说明文档
```

## 🎯 功能页面

### 1. 首页 (home.html)
- ✅ 活动卡片展示（名称、时间、地点、封面图、报名人数）
- ✅ 城市切换功能
- ✅ 搜索和筛选入口
- ✅ 活动标签（热门、新活动、仅剩N位）
- ✅ 下拉刷新模拟
- ✅ 点击跳转活动详情

### 2. 活动详情页 (activity-detail.html)
- ✅ 活动完整信息展示
- ✅ 发布者信息
- ✅ 场地照片
- ✅ 参与人员列表
- ✅ 悬浮操作按钮（申请参与、预订场地）
- ✅ 分享功能

### 3. 预订场地页 (booking.html)
- ✅ 筛选条件（城市、运动类型、价格、距离）
- ✅ 场地列表展示
- ✅ 场地标签和评分
- ✅ 地图模式入口
- ✅ 点击跳转场地详情

### 4. 我的页面 (profile.html)
- ✅ 用户信息展示
- ✅ 三个Tab（我发布的、我申请的、我参与的）
- ✅ 活动状态管理
- ✅ 操作按钮（编辑、取消、撤回等）
- ✅ 设置入口

### 5. 申请参与活动页 (join-activity.html)
- ✅ 活动信息复用展示
- ✅ 活动要求说明
- ✅ 技能水平选择
- ✅ 备注信息填写
- ✅ 表单验证
- ✅ 防重复提交

### 6. 场地详情页 (venue-detail.html)
- ✅ 场地完整信息
- ✅ 设施展示
- ✅ 日期和时间段选择
- ✅ 用户评价
- ✅ 预订功能
- ✅ 分享功能

## 🎨 设计特色

### iPhone 15 Pro 真实感
- 🔲 393x852px 屏幕尺寸
- 🔘 47px 圆角边框
- 📱 状态栏和Home Indicator
- 🖤 设备外观阴影效果

### TDesign 设计风格
- 🎨 统一的色彩体系（主色：#0052d9）
- 📐 12px 圆角卡片设计
- 🔤 规范的字体层级
- 🎯 一致的交互反馈

### 移动端优化
- 👆 触摸友好的按钮尺寸
- 📱 响应式布局设计
- 🔄 流畅的过渡动画
- 💫 悬浮操作按钮

## 🚀 使用方法

1. **直接访问**: 打开 `index.html` 即可查看完整原型
2. **独立页面**: 每个页面都可以独立访问
3. **导航切换**: 使用底部导航在主要页面间切换
4. **功能测试**: 点击各种按钮体验交互效果

## 📋 功能清单

### 已实现功能 ✅
- [x] iPhone 15 Pro 外观设计
- [x] 首页活动列表
- [x] 活动详情展示
- [x] 场地预订流程
- [x] 用户个人中心
- [x] 申请参与活动
- [x] 场地详情和预订
- [x] 底部导航切换
- [x] 响应式设计
- [x] TDesign 风格统一

### 模拟功能 🔄
- [x] 城市切换
- [x] 搜索筛选
- [x] 下拉刷新
- [x] 地图模式
- [x] 分享功能
- [x] 表单提交
- [x] 状态管理

## 🎯 技术亮点

1. **纯前端实现**: 无需后端服务，直接打开即用
2. **模块化设计**: 每个页面独立，便于维护
3. **真实感强**: 高度还原iPhone设备外观
4. **交互完整**: 涵盖所有主要用户流程
5. **设计规范**: 严格遵循TDesign设计规范

## 📝 注意事项

- 本原型为静态演示，所有数据为模拟数据
- 表单提交等功能为模拟实现，会显示提示信息
- 建议使用现代浏览器访问以获得最佳体验
- 页面间跳转使用 `window.open` 新窗口方式

## 🔮 后续扩展

- 添加更多运动类型
- 实现真实的地图集成
- 添加消息通知功能
- 完善用户设置页面
- 集成支付流程
- 添加评论评分系统