/* pages/publish-activity/publish-activity.wxss */
.publish-container {
  width: 100%;
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
}

.content-scroll {
  flex: 1;
  padding-top: 88rpx; /* navbar height */
}

/* 表单区域 */
.form-section {
  background-color: #fff;
  margin-bottom: 16rpx;
  padding: 32rpx 24rpx;
  box-sizing: border-box;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #262626;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.form-item {
  margin-bottom: 32rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 32rpx;
  color: #262626;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.required {
  color: #ff4d4f;
  font-size: 28rpx;
}

/* 选择器字段 */
.picker-field {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 16rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e7e7e7;
  transition: all 0.2s ease;
}

.picker-field:active {
  background-color: #f0f0f0;
}

.placeholder {
  color: #8a8a8a;
  font-size: 28rpx;
}

.selected {
  color: #262626;
  font-size: 28rpx;
}

/* 时间选择器组 */
.time-picker-group {
  display: flex;
  gap: 16rpx;
}

.time-picker-group .picker-field {
  flex: 1;
}

/* 底部占位 */
.bottom-placeholder {
  height: 160rpx;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background-color: #fff;
  padding: 24rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #e7e7e7;
  display: flex;
  gap: 16rpx;
  box-sizing: border-box;
}

.action-btn {
  flex: 1;
}

/* 选择器弹窗 */
.picker-popup {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 60vh;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 24rpx 16rpx;
  border-bottom: 1rpx solid #e7e7e7;
}

.picker-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #262626;
}

.picker-view {
  height: 400rpx;
}

.picker-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  height: 80rpx;
  font-size: 32rpx;
  color: #262626;
}

.sport-icon {
  font-size: 40rpx;
}

.sport-name {
  font-size: 32rpx;
}