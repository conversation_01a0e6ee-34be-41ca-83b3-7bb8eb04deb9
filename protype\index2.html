<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>约球小程序原型 - 全览模式</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/@tdesign/web@1.6.7/dist/tdesign.min.css" rel="stylesheet">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        /* iPhone 15 Pro 屏幕尺寸: 393x852px */
        .phone-container {
            width: 393px;
            height: 852px;
            background: #000;
            border-radius: 47px;
            padding: 8px;
            box-shadow: 0 0 30px rgba(0,0,0,0.5);
            margin: 0 auto 40px;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .phone-container:hover {
            transform: scale(1.02);
            box-shadow: 0 0 40px rgba(0,0,0,0.7);
        }
        
        .phone-screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 39px;
            overflow: hidden;
            position: relative;
        }
        
        .status-bar {
            height: 44px;
            background: #fff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            z-index: 10;
            border-radius: 39px 39px 0 0;
        }
        
        .home-indicator {
            position: absolute;
            bottom: 8px;
            left: 50%;
            transform: translateX(-50%);
            width: 134px;
            height: 5px;
            background: #000;
            border-radius: 3px;
            z-index: 10;
        }
        
        .page-iframe {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 39px;
        }
        
        .page-title {
            text-align: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .page-description {
            text-align: center;
            color: rgba(255,255,255,0.9);
            font-size: 14px;
            margin-bottom: 30px;
            max-width: 350px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.5;
        }
        
        .grid-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(393px, 1fr));
            gap: 60px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .page-section {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .main-title {
            text-align: center;
            color: white;
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 20px;
            text-shadow: 0 2px 8px rgba(0,0,0,0.3);
        }
        
        .main-subtitle {
            text-align: center;
            color: rgba(255,255,255,0.9);
            font-size: 18px;
            margin-bottom: 50px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.6;
        }
        
        .feature-badge {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            margin: 0 4px 8px 0;
            display: inline-block;
            backdrop-filter: blur(10px);
        }
        
        .page-features {
            text-align: center;
            margin-top: 20px;
        }
        
        @media (max-width: 480px) {
            .phone-container {
                width: 350px;
                height: 758px;
            }
            
            .grid-container {
                grid-template-columns: 1fr;
                gap: 40px;
                padding: 0 10px;
            }
            
            body {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- 主标题区域 -->
    <div class="main-title">约球小程序原型展示</div>
    <div class="main-subtitle">
        基于PRC文档设计，采用iPhone 15 Pro尺寸规格和TDesign设计风格<br>
        所有页面平铺展示，便于整体预览和设计评审
    </div>
    
    <!-- 页面网格容器 -->
    <div class="grid-container">
        <!-- 首页 -->
        <div class="page-section">
            <div class="page-title">🏠 首页</div>
            <div class="page-description">
                活动卡片展示，支持城市切换、搜索筛选，展示热门活动和新活动标签
            </div>
            <div class="phone-container">
                <div class="phone-screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>📶 📶 📶 🔋</span>
                    </div>
                    <iframe src="home.html" class="page-iframe"></iframe>
                    <div class="home-indicator"></div>
                </div>
            </div>
            <div class="page-features">
                <span class="feature-badge">活动列表</span>
                <span class="feature-badge">城市切换</span>
                <span class="feature-badge">搜索筛选</span>
                <span class="feature-badge">活动标签</span>
            </div>
        </div>
        
        <!-- 活动详情页 -->
        <div class="page-section">
            <div class="page-title">📋 活动详情</div>
            <div class="page-description">
                完整的活动信息展示，包含发布者信息、场地照片、参与人员列表和操作按钮
            </div>
            <div class="phone-container">
                <div class="phone-screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>📶 📶 📶 🔋</span>
                    </div>
                    <iframe src="activity-detail.html" class="page-iframe"></iframe>
                    <div class="home-indicator"></div>
                </div>
            </div>
            <div class="page-features">
                <span class="feature-badge">活动信息</span>
                <span class="feature-badge">发布者</span>
                <span class="feature-badge">参与人员</span>
                <span class="feature-badge">悬浮按钮</span>
            </div>
        </div>
        
        <!-- 预订场地页 -->
        <div class="page-section">
            <div class="page-title">📍 预订场地</div>
            <div class="page-description">
                场地筛选和列表展示，支持按城市、运动类型、价格和距离筛选场地
            </div>
            <div class="phone-container">
                <div class="phone-screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>📶 📶 📶 🔋</span>
                    </div>
                    <iframe src="booking.html" class="page-iframe"></iframe>
                    <div class="home-indicator"></div>
                </div>
            </div>
            <div class="page-features">
                <span class="feature-badge">场地筛选</span>
                <span class="feature-badge">场地列表</span>
                <span class="feature-badge">地图模式</span>
                <span class="feature-badge">评分展示</span>
            </div>
        </div>
        
        <!-- 我的页面 -->
        <div class="page-section">
            <div class="page-title">👤 我的</div>
            <div class="page-description">
                用户个人中心，包含三个Tab管理不同状态的活动，支持各种操作
            </div>
            <div class="phone-container">
                <div class="phone-screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>📶 📶 📶 🔋</span>
                    </div>
                    <iframe src="profile.html" class="page-iframe"></iframe>
                    <div class="home-indicator"></div>
                </div>
            </div>
            <div class="page-features">
                <span class="feature-badge">用户信息</span>
                <span class="feature-badge">三个Tab</span>
                <span class="feature-badge">状态管理</span>
                <span class="feature-badge">操作按钮</span>
            </div>
        </div>
        
        <!-- 申请参与活动页 -->
        <div class="page-section">
            <div class="page-title">✋ 申请参与</div>
            <div class="page-description">
                申请参与活动的表单页面，包含活动信息展示、技能选择和备注填写
            </div>
            <div class="phone-container">
                <div class="phone-screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>📶 📶 📶 🔋</span>
                    </div>
                    <iframe src="join-activity.html" class="page-iframe"></iframe>
                    <div class="home-indicator"></div>
                </div>
            </div>
            <div class="page-features">
                <span class="feature-badge">活动信息</span>
                <span class="feature-badge">技能选择</span>
                <span class="feature-badge">表单验证</span>
                <span class="feature-badge">提交申请</span>
            </div>
        </div>
        
        <!-- 场地详情页 -->
        <div class="page-section">
            <div class="page-title">🏟️ 场地详情</div>
            <div class="page-description">
                场地完整信息展示，包含设施介绍、时间选择、用户评价和预订功能
            </div>
            <div class="phone-container">
                <div class="phone-screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>📶 📶 📶 🔋</span>
                    </div>
                    <iframe src="venue-detail.html" class="page-iframe"></iframe>
                    <div class="home-indicator"></div>
                </div>
            </div>
            <div class="page-features">
                <span class="feature-badge">场地信息</span>
                <span class="feature-badge">设施展示</span>
                <span class="feature-badge">时间选择</span>
                <span class="feature-badge">用户评价</span>
            </div>
        </div>
    </div>
    
    <!-- 底部说明 -->
    <div style="text-align: center; margin-top: 60px; color: rgba(255,255,255,0.8);">
        <p style="font-size: 14px; margin-bottom: 10px;">
            🎨 采用TDesign设计规范 | 📱 iPhone 15 Pro尺寸 (393x852px) | 🔄 响应式设计
        </p>
        <p style="font-size: 12px;">
            所有页面均为静态原型演示，数据为模拟数据
        </p>
    </div>

    <script>
        // 页面加载完成后的处理
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有iframe添加加载完成的处理
            const iframes = document.querySelectorAll('.page-iframe');
            
            iframes.forEach((iframe, index) => {
                iframe.onload = function() {
                    // iframe加载完成后的处理
                    console.log(`页面 ${index + 1} 加载完成`);
                };
                
                // 错误处理
                iframe.onerror = function() {
                    console.error(`页面 ${index + 1} 加载失败`);
                    iframe.style.background = '#f8f9fa';
                    iframe.style.display = 'flex';
                    iframe.style.alignItems = 'center';
                    iframe.style.justifyContent = 'center';
                };
            });
            
            // 添加平滑滚动效果
            document.documentElement.style.scrollBehavior = 'smooth';
        });
        
        // 鼠标悬停效果增强
        document.querySelectorAll('.phone-container').forEach(container => {
            container.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.02)';
            });
            
            container.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>