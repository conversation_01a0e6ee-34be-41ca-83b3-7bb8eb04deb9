/**app.wxss**/
page {
  width: 100%;
  height: 100vh;
  background: #f5f5f5;
  overflow-x: hidden;
  box-sizing: border-box;
}

view, text, image, button, input, textarea, scroll-view, swiper, swiper-item {
  box-sizing: border-box;
}

.container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
} 

button {
  background: initial;
  box-sizing: border-box;
}

button:focus{
  outline: 0;
}

button::after{
  border: none;
}