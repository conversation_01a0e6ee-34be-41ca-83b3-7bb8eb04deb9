/**
 * 约球小程序 - 全局样式
 * Global Styles for Sports Activity Mini Program
 */

/* 导入设计系统 */
@import "styles/design-system.wxss";
@import "styles/components.wxss";
@import "styles/animations.wxss";
@import "styles/sports-theme.wxss";

/* ========================================
   全局基础样式
   ======================================== */

page {
  width: 100%;
  height: 100vh;
  background: var(--bg-primary);
  overflow-x: hidden;
  box-sizing: border-box;
  font-family: var(--font-family-primary);
  color: var(--text-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

view, text, image, button, input, textarea, scroll-view, swiper, swiper-item {
  box-sizing: border-box;
}

.container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
}

button {
  background: initial;
  box-sizing: border-box;
  border: none;
  outline: none;
  font-family: inherit;
}

button:focus {
  outline: 0;
}

button::after {
  border: none;
}

/* ========================================
   全局布局样式
   ======================================== */

.page-container {
  min-height: 100vh;
  background: var(--bg-primary);
  position: relative;
}

.content-wrapper {
  padding-bottom: 160rpx; /* 为底部导航留出空间 */
}

.section {
  margin-bottom: var(--space-xl);
}

.section-title {
  font-size: var(--font-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
  padding: 0 var(--space-lg);
}

.section-subtitle {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  margin-bottom: var(--space-md);
  padding: 0 var(--space-lg);
}

/* ========================================
   全局交互样式
   ======================================== */

.clickable {
  transition: all var(--duration-fast) var(--easing-ease);
  cursor: pointer;
}

.clickable:active {
  transform: scale(0.98);
  opacity: 0.8;
}

.disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* ========================================
   全局状态样式
   ======================================== */

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-3xl);
  color: var(--text-secondary);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-3xl);
  text-align: center;
}

.empty-state-icon {
  font-size: var(--font-4xl);
  color: var(--text-tertiary);
  margin-bottom: var(--space-lg);
}

.empty-state-title {
  font-size: var(--font-lg);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  margin-bottom: var(--space-sm);
}

.empty-state-desc {
  font-size: var(--font-sm);
  color: var(--text-tertiary);
  line-height: var(--leading-relaxed);
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-3xl);
  text-align: center;
}

.error-state-icon {
  font-size: var(--font-4xl);
  color: var(--color-danger);
  margin-bottom: var(--space-lg);
}