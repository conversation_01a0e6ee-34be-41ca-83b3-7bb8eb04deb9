/**index.wxss**/

page {
  padding-top: 54rpx;
  background-color: #f6f6f6;
  padding-bottom: 60rpx;
}

.container {
  font-family: PingFang SC;
}

.title {
  font-family: PingFang SC;
  font-weight: 500;
  color: #000000;
  font-size: 44rpx;
  margin-bottom: 40rpx;
}
.function_title {
  font-family: PingFang SC;
  font-weight: 500;
  color: #000000;
  font-size: 36rpx;
  text-align: left;
  width: 93%;
  margin-top: 50rpx;
}

.top_tip {
  font-size: 28rpx;
  font-family: PingFang SC;
  font-weight: 400;
  color: #888888;
  margin-bottom: 28rpx;
}

.examples_container {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  width: 100%;
  align-items: center;
}

.example_item {
  border: 3rpx solid #e5e5e5;
  border-radius: 10rpx;
  padding: 24rpx;
  width: 90%;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.power {
  margin-top: 30rpx;
  border-radius: 5px;
  background-color: white;
  width: 93%;
  padding-bottom: 1rpx;
}

.power_info {
  display: flex;
  padding: 30rpx 25rpx;
  align-items: center;
  justify-content: space-between;
}

.power_info_more {
  width: 30rpx;
  height: 30rpx;
  transform: rotate(90deg);
}

.power_info_less {
  width: 30rpx;
  height: 30rpx;
  transform: rotate(270deg);
}

.power_info_text {
  display: flex;
  flex-direction: column;
}

.power_info_text_title {
  margin-bottom: 10rpx;
  font-weight: 400;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  font-family: 'PingFang SC';
  color: #000;
}

.power_info_text_tag {
  margin-left: 20rpx;
  background-color: #fbe0e0;
  color: #e54545;
  padding: 0 7px;
  font-size: 14px;
  vertical-align: middle;
  border-radius: 3px;
}

.power_info_text_tip {
  color: rgba(0, 0, 0, 0.6);
  font-size: 28rpx;
  padding-right: 30rpx;
}

.power_item {
  padding: 30rpx 25rpx;
  display: flex;
  justify-content: space-between;
}

.power_item_title {
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.9);
}

.power_item_icon {
  width: 30rpx;
  height: 30rpx;
}

.line {
  width: 95%;
  margin: 0 auto;
  height: 2rpx;
  background-color: rgba(0, 0, 0, 0.1);
}

.environment {
  color: rgba(0, 0, 0, 0.4);
  font-size: 24rpx;
  margin-top: 25%;
}
