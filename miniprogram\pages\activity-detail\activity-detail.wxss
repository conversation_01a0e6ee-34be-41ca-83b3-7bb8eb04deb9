/* pages/activity-detail/activity-detail.wxss */
.activity-detail-container {
  width: 100%;
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
}

.content-scroll {
  flex: 1;
  padding-top: 88rpx; /* navbar height */
}

/* 活动头部信息 */
.activity-header {
  width: 100%;
  background-color: #fff;
  padding: 32rpx 24rpx;
  margin-bottom: 16rpx;
  box-sizing: border-box;
}

.activity-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #262626;
  margin-bottom: 12rpx;
}

.activity-subtitle {
  font-size: 28rpx;
  color: #8a8a8a;
  margin-bottom: 32rpx;
}

.activity-meta {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.meta-row {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.meta-text {
  font-size: 32rpx;
  color: #262626;
  flex: 1;
}

.location-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.venue-name {
  font-size: 32rpx;
  color: #262626;
  font-weight: 500;
}

.venue-address {
  font-size: 24rpx;
  color: #8a8a8a;
}

/* 场地图片 */
.venue-images {
  background-color: #fff;
  margin-bottom: 16rpx;
}

.image-swiper {
  height: 400rpx;
}

.image-item {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.venue-emoji {
  font-size: 120rpx;
}

/* 活动描述 */
.activity-description {
  width: 100%;
  background-color: #fff;
  padding: 32rpx 24rpx;
  margin-bottom: 16rpx;
  box-sizing: border-box;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #262626;
  margin-bottom: 24rpx;
}

.description-content {
  font-size: 28rpx;
  line-height: 1.6;
  color: #595959;
  white-space: pre-line;
}

.description-content.collapsed {
  max-height: 200rpx;
  overflow: hidden;
  position: relative;
}

.description-content.collapsed::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60rpx;
  background: linear-gradient(transparent, #fff);
}

.toggle-description {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  margin-top: 16rpx;
  padding: 16rpx;
  color: #0052d9;
  font-size: 28rpx;
}

/* 组织者信息 */
.organizer-section {
  width: 100%;
  background-color: #fff;
  padding: 32rpx 24rpx;
  margin-bottom: 16rpx;
  box-sizing: border-box;
}

.organizer-card {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 24rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
}

.organizer-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background-color: #e7e7e7;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
}

.organizer-info {
  flex: 1;
}

.organizer-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #262626;
  margin-bottom: 8rpx;
}

.organizer-level {
  font-size: 24rpx;
  color: #8a8a8a;
}

/* 场地设施 */
.facilities-section {
  width: 100%;
  background-color: #fff;
  padding: 32rpx 24rpx;
  margin-bottom: 16rpx;
  box-sizing: border-box;
}

.facilities-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.facility-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 28rpx;
  color: #262626;
}

/* 参与人员 */
.participants-section {
  width: 100%;
  background-color: #fff;
  padding: 32rpx 24rpx;
  margin-bottom: 16rpx;
  box-sizing: border-box;
}

.participants-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.participant-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.participant-item.empty {
  opacity: 0.6;
}

.participant-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: #e7e7e7;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

.empty-avatar {
  background-color: #d9d9d9;
  color: #8a8a8a;
  font-size: 24rpx;
}

.participant-info {
  flex: 1;
}

.participant-name {
  font-size: 28rpx;
  color: #262626;
  margin-bottom: 4rpx;
}

.participant-level {
  font-size: 22rpx;
  color: #8a8a8a;
}

/* 底部占位 */
.bottom-placeholder {
  height: 160rpx;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background-color: #fff;
  padding: 24rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #e7e7e7;
  display: flex;
  align-items: center;
  gap: 24rpx;
  box-sizing: border-box;
}

.action-buttons {
  flex: 1;
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
}

.share-button {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background-color: #f2f6ff;
  display: flex;
  align-items: center;
  justify-content: center;
}