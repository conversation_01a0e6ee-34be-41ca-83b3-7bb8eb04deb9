<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预订场地</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .venue-card {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            margin-bottom: 12px;
            overflow: hidden;
            transition: all 0.2s;
            cursor: pointer;
        }
        
        .venue-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.12);
        }
        
        .venue-image {
            width: 100px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            flex-shrink: 0;
        }
        
        .filter-chip {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 20px;
            padding: 6px 12px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            white-space: nowrap;
        }
        
        .filter-chip.active {
            background: #0052d9;
            color: white;
            border-color: #0052d9;
        }
        
        .filter-chip:hover {
            border-color: #0052d9;
        }
        
        .price-range {
            background: #f2f3ff;
            color: #0052d9;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .venue-tag {
            background: #fff3cd;
            color: #856404;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            margin-right: 4px;
        }
        
        .venue-tag.recommended {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .map-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: #0052d9;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 16px rgba(0,0,0,0.2);
            transition: all 0.2s;
        }
        
        .map-btn:hover {
            transform: scale(1.1);
        }
        
        .distance-badge {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="h-full overflow-y-auto">
        <!-- 顶部筛选区域 -->
        <div class="bg-white p-4 border-b border-gray-100">
            <h1 class="text-lg font-bold mb-4">预订场地</h1>
            
            <!-- 城市选择 -->
            <div class="mb-3">
                <div class="flex items-center gap-2 mb-2">
                    <span class="text-sm font-medium text-gray-700">城市</span>
                </div>
                <div class="flex gap-2">
                    <div class="filter-chip active">上海</div>
                    <div class="filter-chip">北京</div>
                    <div class="filter-chip">深圳</div>
                    <div class="filter-chip">杭州</div>
                </div>
            </div>
            
            <!-- 运动类型 -->
            <div class="mb-3">
                <div class="flex items-center gap-2 mb-2">
                    <span class="text-sm font-medium text-gray-700">运动类型</span>
                </div>
                <div class="flex gap-2 overflow-x-auto pb-2">
                    <div class="filter-chip active">全部</div>
                    <div class="filter-chip">🏸 羽毛球</div>
                    <div class="filter-chip">🏀 篮球</div>
                    <div class="filter-chip">🎾 网球</div>
                    <div class="filter-chip">⚽ 足球</div>
                    <div class="filter-chip">🏓 乒乓球</div>
                </div>
            </div>
            
            <!-- 价格和距离 -->
            <div class="flex gap-4">
                <div class="flex-1">
                    <span class="text-sm font-medium text-gray-700">价格区间</span>
                    <div class="flex gap-2 mt-2">
                        <div class="filter-chip active">不限</div>
                        <div class="filter-chip">50以下</div>
                        <div class="filter-chip">50-100</div>
                    </div>
                </div>
                <div class="flex-1">
                    <span class="text-sm font-medium text-gray-700">距离</span>
                    <div class="flex gap-2 mt-2">
                        <div class="filter-chip active">不限</div>
                        <div class="filter-chip">3km内</div>
                        <div class="filter-chip">5km内</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 场地列表 -->
        <div class="px-4 py-4">
            <!-- 场地卡片1 -->
            <div class="venue-card" onclick="openVenueDetail(1)">
                <div class="flex p-4">
                    <div class="venue-image" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                        🏸
                    </div>
                    <div class="flex-1 ml-3">
                        <div class="flex items-start justify-between mb-2">
                            <h3 class="font-semibold text-base">浦东新区羽毛球馆</h3>
                            <div class="text-right">
                                <div class="text-lg font-bold text-orange-500">¥45</div>
                                <div class="text-xs text-gray-500">人均/小时</div>
                            </div>
                        </div>
                        
                        <div class="flex items-center gap-2 mb-2">
                            <div class="venue-tag recommended">推荐</div>
                            <div class="venue-tag">设施完善</div>
                            <div class="distance-badge">2.3km</div>
                        </div>
                        
                        <div class="text-sm text-gray-600 mb-2">
                            📍 浦东新区张江路123号
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-500">
                                ⭐ 4.8分 (128条评价)
                            </div>
                            <div class="price-range">
                                今日可预订
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 场地卡片2 -->
            <div class="venue-card" onclick="openVenueDetail(2)">
                <div class="flex p-4">
                    <div class="venue-image" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                        🏀
                    </div>
                    <div class="flex-1 ml-3">
                        <div class="flex items-start justify-between mb-2">
                            <h3 class="font-semibold text-base">静安区体育中心</h3>
                            <div class="text-right">
                                <div class="text-lg font-bold text-orange-500">¥30</div>
                                <div class="text-xs text-gray-500">人均/小时</div>
                            </div>
                        </div>
                        
                        <div class="flex items-center gap-2 mb-2">
                            <div class="venue-tag">室外场地</div>
                            <div class="venue-tag">免费停车</div>
                            <div class="distance-badge">1.8km</div>
                        </div>
                        
                        <div class="text-sm text-gray-600 mb-2">
                            📍 静安区南京西路456号
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-500">
                                ⭐ 4.6分 (89条评价)
                            </div>
                            <div class="price-range">
                                今日可预订
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 场地卡片3 -->
            <div class="venue-card" onclick="openVenueDetail(3)">
                <div class="flex p-4">
                    <div class="venue-image" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                        🎾
                    </div>
                    <div class="flex-1 ml-3">
                        <div class="flex items-start justify-between mb-2">
                            <h3 class="font-semibold text-base">徐汇区网球俱乐部</h3>
                            <div class="text-right">
                                <div class="text-lg font-bold text-orange-500">¥80</div>
                                <div class="text-xs text-gray-500">人均/小时</div>
                            </div>
                        </div>
                        
                        <div class="flex items-center gap-2 mb-2">
                            <div class="venue-tag recommended">推荐</div>
                            <div class="venue-tag">专业场地</div>
                            <div class="venue-tag">教练服务</div>
                            <div class="distance-badge">3.5km</div>
                        </div>
                        
                        <div class="text-sm text-gray-600 mb-2">
                            📍 徐汇区漕河泾开发区789号
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-500">
                                ⭐ 4.9分 (256条评价)
                            </div>
                            <div class="price-range">
                                今日可预订
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 场地卡片4 -->
            <div class="venue-card" onclick="openVenueDetail(4)">
                <div class="flex p-4">
                    <div class="venue-image" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                        ⚽
                    </div>
                    <div class="flex-1 ml-3">
                        <div class="flex items-start justify-between mb-2">
                            <h3 class="font-semibold text-base">黄浦区足球场</h3>
                            <div class="text-right">
                                <div class="text-lg font-bold text-orange-500">¥25</div>
                                <div class="text-xs text-gray-500">人均/小时</div>
                            </div>
                        </div>
                        
                        <div class="flex items-center gap-2 mb-2">
                            <div class="venue-tag">天然草坪</div>
                            <div class="venue-tag">夜间照明</div>
                            <div class="distance-badge">4.2km</div>
                        </div>
                        
                        <div class="text-sm text-gray-600 mb-2">
                            📍 黄浦区人民广场附近
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-500">
                                ⭐ 4.7分 (167条评价)
                            </div>
                            <div class="price-range">
                                今日可预订
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 加载更多 -->
            <div class="text-center py-4">
                <button class="text-gray-500 text-sm" onclick="loadMore()">
                    加载更多场地...
                </button>
            </div>
        </div>
    </div>
    
    <!-- 地图按钮 -->
    <div class="map-btn" onclick="showMap()" title="地图模式">
        🗺️
    </div>

    <script>
        function openVenueDetail(id) {
            window.open(`venue-detail.html?id=${id}`, '_blank');
        }
        
        function showMap() {
            alert('地图模式功能');
        }
        
        function loadMore() {
            alert('加载更多场地');
        }
        
        // 筛选功能
        document.querySelectorAll('.filter-chip').forEach(chip => {
            chip.addEventListener('click', function() {
                // 移除同组其他选项的active状态
                const parent = this.parentElement;
                parent.querySelectorAll('.filter-chip').forEach(c => c.classList.remove('active'));
                // 添加当前选项的active状态
                this.classList.add('active');
            });
        });
    </script>
</body>
</html>