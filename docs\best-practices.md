# 约球小程序 - 设计系统最佳实践

## 🎯 总体原则

### 1. 一致性优先
- 始终使用设计系统中定义的组件和样式
- 避免创建临时的、不符合规范的样式
- 保持整个应用的视觉和交互一致性

### 2. 性能考虑
- 合理使用动画，避免过度动效影响性能
- 优先使用CSS变量，便于主题切换和维护
- 避免重复定义相同的样式

### 3. 可维护性
- 使用语义化的类名和组件
- 遵循模块化的样式组织方式
- 及时更新和维护设计系统

## 🎨 色彩使用指南

### ✅ 推荐做法

```css
/* 使用设计系统中的色彩变量 */
.custom-component {
  background: var(--primary-gradient);
  color: var(--text-inverse);
  border: 1rpx solid var(--border-light);
}

/* 使用运动主题色彩 */
.badminton-section {
  background: var(--sport-badminton);
}
```

### ❌ 避免做法

```css
/* 避免硬编码颜色值 */
.bad-component {
  background: #667eea; /* 应该使用 var(--sport-badminton) */
  color: #ffffff;      /* 应该使用 var(--text-inverse) */
}

/* 避免使用过多不同的颜色 */
.too-many-colors {
  background: linear-gradient(45deg, #ff0000, #00ff00, #0000ff, #ffff00);
}
```

### 色彩搭配建议

1. **主色调使用**
   - 主要操作按钮使用 `--primary-gradient`
   - 重要信息使用 `--color-primary`
   - 品牌元素使用主色调

2. **运动主题色使用**
   - 根据运动类型选择对应的主题色
   - 在同一页面中保持运动主题的一致性
   - 使用主题色的浅色版本作为背景

3. **中性色使用**
   - 文字内容使用中性色系
   - 背景和边框使用浅色中性色
   - 避免大面积使用深色

## 📝 字体使用指南

### ✅ 推荐做法

```css
/* 使用设计系统中的字体大小 */
.title {
  font-size: var(--font-2xl);
  font-weight: var(--font-bold);
  line-height: var(--leading-tight);
}

.body-text {
  font-size: var(--font-base);
  font-weight: var(--font-normal);
  line-height: var(--leading-normal);
}
```

### ❌ 避免做法

```css
/* 避免使用任意的字体大小 */
.bad-title {
  font-size: 42rpx; /* 应该使用预定义的字体大小 */
}

/* 避免过多的字体粗细变化 */
.inconsistent-weights {
  font-weight: 350; /* 应该使用预定义的字体粗细 */
}
```

### 字体层级建议

1. **标题层级**
   - H1: `--font-3xl` + `--font-bold`
   - H2: `--font-2xl` + `--font-bold`
   - H3: `--font-xl` + `--font-semibold`
   - H4: `--font-lg` + `--font-medium`

2. **正文层级**
   - 主要正文: `--font-base` + `--font-normal`
   - 次要正文: `--font-sm` + `--font-normal`
   - 辅助信息: `--font-xs` + `--font-normal`

## 📐 间距使用指南

### ✅ 推荐做法

```css
/* 使用设计系统中的间距变量 */
.component {
  padding: var(--space-lg);
  margin-bottom: var(--space-xl);
  gap: var(--space-md);
}

/* 使用工具类 */
.utility-spacing {
  @apply p-lg m-xl;
}
```

### ❌ 避免做法

```css
/* 避免使用任意的间距值 */
.bad-spacing {
  padding: 18rpx; /* 应该使用预定义的间距 */
  margin: 25rpx;  /* 应该使用预定义的间距 */
}
```

### 间距使用建议

1. **组件内部间距**
   - 小组件: `--space-sm` 到 `--space-md`
   - 中等组件: `--space-md` 到 `--space-lg`
   - 大组件: `--space-lg` 到 `--space-xl`

2. **组件之间间距**
   - 相关元素: `--space-md`
   - 独立组件: `--space-lg` 到 `--space-xl`
   - 页面区块: `--space-2xl` 到 `--space-3xl`

## 🔘 圆角使用指南

### ✅ 推荐做法

```css
/* 根据组件大小选择合适的圆角 */
.small-component {
  border-radius: var(--radius-sm);
}

.large-component {
  border-radius: var(--radius-lg);
}

.circular-element {
  border-radius: var(--radius-full);
}
```

### 圆角使用建议

1. **按钮圆角**
   - 小按钮: `--radius-md`
   - 标准按钮: `--radius-lg`
   - 大按钮: `--radius-xl`
   - 圆形按钮: `--radius-full`

2. **卡片圆角**
   - 小卡片: `--radius-md`
   - 标准卡片: `--radius-lg`
   - 大卡片: `--radius-xl`

3. **表单元素圆角**
   - 输入框: `--radius-md`
   - 文本域: `--radius-md`
   - 选择器: `--radius-md`

## 🎬 动画使用指南

### ✅ 推荐做法

```css
/* 使用预定义的动画时长和缓动函数 */
.interactive-element {
  transition: all var(--duration-normal) var(--easing-ease);
}

.quick-feedback {
  transition: transform var(--duration-fast) var(--easing-ease);
}

/* 使用预定义的动画类 */
.animated-card {
  @apply card-slide-up;
}
```

### ❌ 避免做法

```css
/* 避免使用过长的动画时长 */
.slow-animation {
  transition: all 2s ease; /* 太慢，影响用户体验 */
}

/* 避免过度使用动画 */
.too-many-animations {
  animation: bounce 1s infinite, rotate 2s infinite, pulse 1.5s infinite;
}
```

### 动画使用建议

1. **交互反馈动画**
   - 按钮点击: `--duration-fast`
   - 悬停效果: `--duration-fast`
   - 状态切换: `--duration-normal`

2. **页面转场动画**
   - 页面切换: `--duration-normal`
   - 模态框显示: `--duration-normal`
   - 抽屉展开: `--duration-normal`

3. **加载动画**
   - 数据加载: 循环动画
   - 骨架屏: 持续动画
   - 进度指示: 平滑过渡

## 🧩 组件使用指南

### ✅ 推荐做法

```html
<!-- 使用预定义的组件类 -->
<button class="btn btn-primary btn-lg" bindtap="onSubmit">
  提交申请
</button>

<!-- 组合使用工具类 -->
<view class="card bg-glass p-lg rounded-xl shadow-md">
  <text class="text-lg font-bold text-primary">卡片标题</text>
  <text class="text-sm text-secondary">卡片内容</text>
</view>

<!-- 使用运动主题组件 -->
<view class="activity-card sport-badminton">
  <view class="activity-card-image">
    <text class="sport-icon">🏸</text>
  </view>
</view>
```

### ❌ 避免做法

```html
<!-- 避免重复定义相同的样式 -->
<view style="background: white; border-radius: 24rpx; padding: 32rpx; box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.12);">
  <!-- 应该使用 card 类 -->
</view>

<!-- 避免不一致的组件使用 -->
<button style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 16rpx 32rpx;">
  <!-- 应该使用 btn btn-primary 类 -->
</button>
```

### 组件组合建议

1. **基础组件 + 修饰符**
   ```html
   <button class="btn btn-primary btn-lg">基础按钮 + 主要样式 + 大尺寸</button>
   ```

2. **组件 + 工具类**
   ```html
   <view class="card p-lg m-md shadow-lg">卡片 + 内边距 + 外边距 + 阴影</view>
   ```

3. **主题 + 组件**
   ```html
   <view class="sport-badminton activity-card">运动主题 + 活动卡片</view>
   ```

## 📱 响应式设计指南

### ✅ 推荐做法

```css
/* 使用相对单位 */
.responsive-component {
  width: 100%;
  max-width: 600rpx;
  padding: var(--space-lg);
}

/* 使用弹性布局 */
.flexible-layout {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-md);
}

.flexible-item {
  flex: 1;
  min-width: 280rpx;
}
```

### 响应式建议

1. **布局适配**
   - 优先使用 Flexbox 和 Grid
   - 设置合理的最小和最大宽度
   - 使用相对单位而非固定像素

2. **内容适配**
   - 文字大小保持可读性
   - 图片使用合适的压缩和尺寸
   - 交互元素保持足够的点击区域

## 🔧 维护和更新指南

### 版本管理
1. **设计系统版本**
   - 主版本: 重大变更和不兼容更新
   - 次版本: 新功能和组件添加
   - 修订版本: 问题修复和小改进

2. **更新流程**
   - 测试新版本的兼容性
   - 更新文档和示例
   - 通知团队成员变更内容

### 质量保证
1. **代码审查**
   - 检查是否遵循设计系统规范
   - 验证组件的可复用性
   - 确保代码质量和性能

2. **测试验证**
   - 在不同设备上测试显示效果
   - 验证交互动画的流畅性
   - 检查可访问性和用户体验

### 文档维护
1. **及时更新**
   - 新增组件时更新文档
   - 修改样式时同步示例
   - 定期检查文档的准确性

2. **示例完善**
   - 提供完整的使用示例
   - 包含常见的使用场景
   - 说明最佳实践和注意事项

## 🎯 性能优化建议

### CSS 优化
1. **减少重复样式**
   - 使用设计系统的变量和类
   - 避免内联样式
   - 合并相似的样式规则

2. **动画优化**
   - 使用 transform 和 opacity 进行动画
   - 避免触发重排和重绘的属性
   - 合理设置动画时长

### 加载优化
1. **按需加载**
   - 只导入需要的样式文件
   - 使用条件加载减少初始包大小
   - 优化关键渲染路径

2. **缓存策略**
   - 利用浏览器缓存
   - 使用版本控制管理资源
   - 压缩和优化样式文件

通过遵循这些最佳实践，可以确保约球小程序的设计系统得到正确和高效的使用，提供一致、优质的用户体验。
