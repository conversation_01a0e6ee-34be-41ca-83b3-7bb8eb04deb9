/**
 * 约球小程序 - 动效系统
 * Animation System for Sports Activity Mini Program
 */

/* ========================================
   页面转场动画
   ======================================== */

.page-enter {
  animation: pageEnter var(--duration-normal) var(--easing-ease) forwards;
}

.page-exit {
  animation: pageExit var(--duration-normal) var(--easing-ease) forwards;
}

@keyframes pageEnter {
  from {
    opacity: 0;
    transform: translateX(100rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pageExit {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-100rpx);
  }
}

/* ========================================
   卡片动画效果
   ======================================== */

.card-slide-up {
  animation: cardSlideUp var(--duration-normal) var(--easing-ease) forwards;
}

@keyframes cardSlideUp {
  from {
    opacity: 0;
    transform: translateY(60rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card-scale-in {
  animation: cardScaleIn var(--duration-normal) var(--easing-bounce) forwards;
}

@keyframes cardScaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.card-flip {
  animation: cardFlip var(--duration-slow) var(--easing-ease) forwards;
}

@keyframes cardFlip {
  0% {
    transform: rotateY(0deg);
  }
  50% {
    transform: rotateY(90deg);
  }
  100% {
    transform: rotateY(0deg);
  }
}

/* ========================================
   按钮交互动画
   ======================================== */

.btn-bounce {
  animation: btnBounce var(--duration-normal) var(--easing-bounce);
}

@keyframes btnBounce {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.btn-shake {
  animation: btnShake var(--duration-normal) var(--easing-ease);
}

@keyframes btnShake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-10rpx);
  }
  75% {
    transform: translateX(10rpx);
  }
}

.btn-ripple {
  position: relative;
  overflow: hidden;
}

.btn-ripple::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width var(--duration-normal), height var(--duration-normal);
}

.btn-ripple:active::after {
  width: 600rpx;
  height: 600rpx;
}

/* ========================================
   加载动画
   ======================================== */

.loading-dots {
  display: inline-flex;
  gap: var(--space-xs);
}

.loading-dots .dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: var(--color-primary);
  animation: loadingDots 1.4s infinite ease-in-out both;
}

.loading-dots .dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dots .dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes loadingDots {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.loading-pulse {
  animation: loadingPulse 2s infinite;
}

@keyframes loadingPulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.loading-skeleton {
  background: linear-gradient(90deg, var(--neutral-200) 25%, var(--neutral-100) 50%, var(--neutral-200) 75%);
  background-size: 400% 100%;
  animation: loadingSkeleton 1.2s ease-in-out infinite;
}

@keyframes loadingSkeleton {
  0% {
    background-position: 100% 0;
  }
  100% {
    background-position: -100% 0;
  }
}

/* ========================================
   浮动动画
   ======================================== */

.float {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20rpx);
  }
}

.float-slow {
  animation: float 6s ease-in-out infinite;
}

/* ========================================
   渐变动画
   ======================================== */

.gradient-shift {
  background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);
  background-size: 400% 400%;
  animation: gradientShift 4s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* ========================================
   弹性动画
   ======================================== */

.bounce-in {
  animation: bounceIn var(--duration-slow) var(--easing-bounce);
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.bounce-out {
  animation: bounceOut var(--duration-normal) var(--easing-ease);
}

@keyframes bounceOut {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(0.95);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(0);
  }
}

/* ========================================
   滑动动画
   ======================================== */

.slide-in-left {
  animation: slideInLeft var(--duration-normal) var(--easing-ease);
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-right {
  animation: slideInRight var(--duration-normal) var(--easing-ease);
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-up {
  animation: slideInUp var(--duration-normal) var(--easing-ease);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-down {
  animation: slideInDown var(--duration-normal) var(--easing-ease);
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ========================================
   旋转动画
   ======================================== */

.rotate {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.rotate-slow {
  animation: rotate 4s linear infinite;
}

/* ========================================
   心跳动画
   ======================================== */

.heartbeat {
  animation: heartbeat 1.5s ease-in-out infinite both;
}

@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.3);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.3);
  }
  70% {
    transform: scale(1);
  }
}

/* ========================================
   闪烁动画
   ======================================== */

.blink {
  animation: blink 1s linear infinite;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

/* ========================================
   动画延迟类
   ======================================== */

.delay-100 { animation-delay: 100ms; }
.delay-200 { animation-delay: 200ms; }
.delay-300 { animation-delay: 300ms; }
.delay-500 { animation-delay: 500ms; }
.delay-700 { animation-delay: 700ms; }
.delay-1000 { animation-delay: 1000ms; }

/* ========================================
   动画持续时间类
   ======================================== */

.duration-75 { animation-duration: 75ms; }
.duration-100 { animation-duration: 100ms; }
.duration-150 { animation-duration: 150ms; }
.duration-200 { animation-duration: 200ms; }
.duration-300 { animation-duration: 300ms; }
.duration-500 { animation-duration: 500ms; }
.duration-700 { animation-duration: 700ms; }
.duration-1000 { animation-duration: 1000ms; }
