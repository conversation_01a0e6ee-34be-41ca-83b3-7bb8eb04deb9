/* pages/venue-detail/venue-detail.wxss */
.venue-detail-container {
  width: 100%;
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
}

.content-scroll {
  flex: 1;
  padding-top: 88rpx; /* navbar height */
}

/* 场地图片 */
.venue-images {
  background-color: #fff;
  margin-bottom: 16rpx;
}

.image-swiper {
  height: 400rpx;
}

.image-item {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.venue-emoji {
  font-size: 120rpx;
}

/* 场地信息 */
.venue-info {
  width: 100%;
  background-color: #fff;
  padding: 32rpx 24rpx;
  margin-bottom: 16rpx;
  box-sizing: border-box;
}

.venue-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.venue-name {
  font-size: 40rpx;
  font-weight: 600;
  color: #262626;
  flex: 1;
}

.venue-rating {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #262626;
}

.venue-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
  margin-bottom: 24rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  color: #8a8a8a;
}

.venue-address {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
  font-size: 28rpx;
  color: #262626;
}

.venue-address text {
  flex: 1;
}

.contact-actions {
  display: flex;
  gap: 16rpx;
}

.contact-actions .t-button {
  flex: 1;
}

/* 场地描述 */
.venue-description {
  width: 100%;
  background-color: #fff;
  padding: 32rpx 24rpx;
  margin-bottom: 16rpx;
  box-sizing: border-box;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #262626;
  margin-bottom: 24rpx;
}

.description-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #595959;
}

/* 设施服务 */
.facilities-section {
  width: 100%;
  background-color: #fff;
  padding: 32rpx 24rpx;
  margin-bottom: 16rpx;
  box-sizing: border-box;
}

.facilities-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.facility-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #262626;
}

.facility-item.unavailable {
  opacity: 0.5;
}

.facility-icon {
  font-size: 32rpx;
}

.facility-name {
  flex: 1;
}

/* 时间段选择 */
.time-slots-section {
  width: 100%;
  background-color: #fff;
  padding: 32rpx 24rpx;
  margin-bottom: 16rpx;
  box-sizing: border-box;
}

.date-selector {
  font-size: 28rpx;
  color: #262626;
  margin-bottom: 24rpx;
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.time-slots-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.time-slot {
  padding: 20rpx;
  border: 2rpx solid #e7e7e7;
  border-radius: 12rpx;
  text-align: center;
  transition: all 0.2s ease;
  position: relative;
}

.time-slot.selected {
  border-color: #0052d9;
  background-color: #f2f6ff;
}

.time-slot.unavailable {
  background-color: #f5f5f5;
  color: #8a8a8a;
  border-color: #d9d9d9;
}

.slot-time {
  font-size: 28rpx;
  font-weight: 500;
  color: #262626;
  margin-bottom: 8rpx;
}

.slot-price {
  font-size: 32rpx;
  font-weight: 600;
  color: #ff6b35;
}

.slot-status {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  font-size: 20rpx;
  color: #ff4d4f;
  background-color: #fff2f0;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

/* 用户评价 */
.reviews-section {
  width: 100%;
  background-color: #fff;
  padding: 32rpx 24rpx;
  margin-bottom: 16rpx;
  box-sizing: border-box;
}

.review-item {
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.review-item:last-child {
  border-bottom: none;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.reviewer-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.reviewer-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: #e7e7e7;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

.reviewer-name {
  font-size: 28rpx;
  color: #262626;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.review-date {
  font-size: 24rpx;
  color: #8a8a8a;
}

.review-rating {
  display: flex;
  gap: 4rpx;
}

.review-content {
  font-size: 28rpx;
  line-height: 1.5;
  color: #595959;
  margin-bottom: 16rpx;
}

.review-images {
  display: flex;
  gap: 12rpx;
}

.review-image {
  width: 80rpx;
  height: 80rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
}

/* 底部占位 */
.bottom-placeholder {
  height: 120rpx;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background-color: #fff;
  padding: 24rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #e7e7e7;
  box-sizing: border-box;
}

/* 预订弹窗 */
.booking-modal {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 32rpx;
  max-height: 60vh;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid #e7e7e7;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #262626;
}

.booking-details {
  margin-bottom: 32rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  font-size: 28rpx;
  color: #262626;
}

.detail-label {
  color: #8a8a8a;
}

.price-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #ff6b35;
}

.modal-actions {
  display: flex;
  gap: 16rpx;
}

.modal-actions .t-button {
  flex: 1;
}