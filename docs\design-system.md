# 约球小程序 - 活力运动设计系统

## 🎨 设计理念

"活力运动"设计系统以年轻、现代、充满活力的视觉语言为核心，通过渐变色彩、圆角设计、微动效等元素，打造符合年轻用户审美的运动社交平台。

## 📋 设计原则

### 1. 活力优先 (Energy First)
- 使用充满活力的渐变色彩
- 采用动态的微动效增强交互感
- 营造积极向上的运动氛围

### 2. 简洁现代 (Modern Simplicity)
- 大圆角设计语言
- 清晰的视觉层次
- 简洁的信息架构

### 3. 一致性 (Consistency)
- 统一的设计令牌系统
- 标准化的组件库
- 规范的交互模式

### 4. 可访问性 (Accessibility)
- 良好的色彩对比度
- 清晰的字体层级
- 友好的交互反馈

## 🎯 色彩系统

### 主色调渐变
```css
--primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
--secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
--success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
--warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
--danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
```

### 运动主题色彩
- **羽毛球**: #667eea (蓝紫色)
- **篮球**: #f5576c (活力红)
- **网球**: #4facfe (天空蓝)
- **足球**: #2ed573 (草地绿)
- **乒乓球**: #ffa502 (橙黄色)
- **跑步**: #ff6b6b (珊瑚红)
- **游泳**: #74b9ff (海洋蓝)
- **骑行**: #a29bfe (薰衣草紫)

### 中性色系
```css
--neutral-50: #fafbfc;   /* 最浅背景 */
--neutral-100: #f4f6f8;  /* 浅背景 */
--neutral-200: #e4e7eb;  /* 边框浅色 */
--neutral-300: #d1d5db;  /* 边框中色 */
--neutral-400: #9ca3af;  /* 辅助文字 */
--neutral-500: #6b7280;  /* 次要文字 */
--neutral-600: #4b5563;  /* 主要文字 */
--neutral-700: #374151;  /* 深色文字 */
--neutral-800: #1f2937;  /* 标题文字 */
--neutral-900: #111827;  /* 最深文字 */
```

## 📝 字体系统

### 字体族
```css
--font-family-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
```

### 字体大小
- **xs**: 24rpx - 辅助信息
- **sm**: 28rpx - 次要文字
- **base**: 32rpx - 正文文字
- **lg**: 36rpx - 小标题
- **xl**: 40rpx - 中标题
- **2xl**: 48rpx - 大标题
- **3xl**: 56rpx - 主标题
- **4xl**: 64rpx - 超大标题

### 字体粗细
- **light**: 300 - 轻量文字
- **normal**: 400 - 常规文字
- **medium**: 500 - 中等文字
- **semibold**: 600 - 半粗体
- **bold**: 700 - 粗体
- **extrabold**: 800 - 超粗体

## 📐 间距系统

```css
--space-xs: 8rpx;    /* 最小间距 */
--space-sm: 12rpx;   /* 小间距 */
--space-md: 16rpx;   /* 中等间距 */
--space-lg: 24rpx;   /* 大间距 */
--space-xl: 32rpx;   /* 超大间距 */
--space-2xl: 48rpx;  /* 2倍超大间距 */
--space-3xl: 64rpx;  /* 3倍超大间距 */
--space-4xl: 96rpx;  /* 4倍超大间距 */
```

## 🔘 圆角系统

```css
--radius-xs: 8rpx;   /* 最小圆角 */
--radius-sm: 12rpx;  /* 小圆角 */
--radius-md: 16rpx;  /* 中等圆角 */
--radius-lg: 24rpx;  /* 大圆角 */
--radius-xl: 32rpx;  /* 超大圆角 */
--radius-full: 9999rpx; /* 完全圆角 */
```

## 🌟 阴影系统

```css
--shadow-xs: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);   /* 最轻阴影 */
--shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);   /* 轻阴影 */
--shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);  /* 中等阴影 */
--shadow-lg: 0 8rpx 32rpx rgba(0, 0, 0, 0.16);  /* 重阴影 */
--shadow-xl: 0 16rpx 64rpx rgba(0, 0, 0, 0.20); /* 最重阴影 */
```

## 🎬 动效系统

### 动画时长
```css
--duration-fast: 150ms;   /* 快速动画 */
--duration-normal: 300ms; /* 标准动画 */
--duration-slow: 500ms;   /* 慢速动画 */
```

### 缓动函数
```css
--easing-ease: cubic-bezier(0.25, 0.46, 0.45, 0.94);     /* 标准缓动 */
--easing-ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19); /* 缓入 */
--easing-ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);   /* 缓出 */
--easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);  /* 弹性 */
```

## 🧩 组件库

### 按钮组件
- **btn-primary**: 主要按钮，使用主色调渐变
- **btn-secondary**: 次要按钮，使用次色调渐变
- **btn-outline**: 轮廓按钮，透明背景带边框
- **btn-ghost**: 幽灵按钮，半透明背景
- **btn-circle**: 圆形按钮，用于浮动操作

### 卡片组件
- **card**: 基础卡片，白色背景带阴影
- **card-glass**: 毛玻璃卡片，半透明背景
- **card-gradient**: 渐变卡片，使用主题渐变
- **activity-card**: 活动专用卡片，带运动主题

### 表单组件
- **form-input**: 基础输入框
- **form-textarea**: 多行文本框
- **form-label**: 表单标签

### 标签组件
- **tag**: 基础标签
- **tag-hot**: 热门标签，红色渐变
- **tag-new**: 新活动标签，绿色渐变
- **tag-limited**: 限量标签，橙色渐变

## 🏃‍♂️ 运动主题

### 运动卡片
每种运动类型都有专属的主题色彩和渐变效果：

```css
.sport-badminton { --sport-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.sport-basketball { --sport-gradient: linear-gradient(135deg, #f5576c 0%, #f093fb 100%); }
.sport-tennis { --sport-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
```

### 运动图标
使用 Emoji 图标系统，简洁直观：
- 🏸 羽毛球
- 🏀 篮球
- 🎾 网球
- ⚽ 足球
- 🏓 乒乓球
- 🏃 跑步
- 🏊 游泳
- 🚴 骑行

## 📱 响应式设计

### 断点系统
- **小屏**: < 375px
- **中屏**: 375px - 414px
- **大屏**: > 414px

### 适配原则
1. 优先适配 iPhone 14/15 系列 (393px)
2. 确保在小屏设备上的可用性
3. 充分利用大屏设备的空间

## 🔧 使用指南

### 1. 导入样式
在页面的 wxss 文件中导入所需样式：

```css
@import "../../styles/design-system.wxss";
@import "../../styles/components.wxss";
@import "../../styles/animations.wxss";
@import "../../styles/sports-theme.wxss";
```

### 2. 使用组件类
```html
<!-- 主要按钮 -->
<button class="btn btn-primary btn-lg">立即参与</button>

<!-- 活动卡片 -->
<view class="activity-card sport-badminton">
  <view class="activity-card-image">
    <text class="sport-icon">🏸</text>
  </view>
  <view class="activity-card-content">
    <text class="sport-card-title">周末羽毛球约战</text>
  </view>
</view>

<!-- 运动标签 -->
<view class="sport-tag sport-tag-hot">🔥 热门</view>
```

### 3. 使用工具类
```html
<!-- 间距和布局 -->
<view class="flex items-center justify-between p-lg">
  <text class="text-lg font-bold">活动标题</text>
  <view class="tag tag-primary">羽毛球</view>
</view>

<!-- 背景和阴影 -->
<view class="bg-card rounded-lg shadow-md p-xl">
  内容区域
</view>
```

## 🎯 最佳实践

### 1. 色彩使用
- 主色调用于重要操作和品牌元素
- 运动主题色用于区分不同运动类型
- 中性色用于文字和背景
- 避免过度使用高饱和度颜色

### 2. 动效使用
- 页面转场使用标准动画时长 (300ms)
- 按钮交互使用快速动画 (150ms)
- 加载状态使用循环动画
- 避免过度动画影响性能

### 3. 组件组合
- 优先使用预定义组件
- 通过工具类进行微调
- 保持组件的一致性
- 遵循设计系统规范

### 4. 可访问性
- 确保足够的色彩对比度
- 提供清晰的交互反馈
- 使用语义化的组件结构
- 考虑不同用户的使用场景
