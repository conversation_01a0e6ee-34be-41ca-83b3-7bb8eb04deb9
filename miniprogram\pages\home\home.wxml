<!--pages/home/<USER>
<view class="page-container page-enter">
  <!-- 顶部渐变背景区域 -->
  <view class="hero-section bg-gradient-primary">
    <view class="hero-content">
      <!-- 顶部导航 -->
      <view class="top-nav">
        <view class="city-selector clickable" bind:tap="onCityTap">
          <text class="location-icon">📍</text>
          <text class="city-name text-inverse font-medium">{{currentCity}}</text>
          <text class="arrow-icon text-inverse">▼</text>
        </view>
        <button class="btn btn-ghost btn-sm" bind:tap="onFilterTap">
          <text>筛选</text>
        </button>
      </view>

      <!-- 欢迎标题 -->
      <view class="hero-title">
        <text class="title-main text-inverse font-bold">发现精彩运动</text>
        <text class="title-sub text-inverse">和志同道合的伙伴一起挥洒汗水</text>
      </view>

      <!-- 搜索框 -->
      <view class="search-container">
        <view class="search-box bg-glass rounded-xl">
          <text class="search-icon">🔍</text>
          <input
            class="search-input"
            placeholder="搜索活动、场地..."
            value="{{searchValue}}"
            bind:input="onSearchInput"
            bind:confirm="onSearchConfirm"
          />
        </view>
      </view>
    </view>

    <!-- 装饰性元素 -->
    <view class="hero-decoration">
      <view class="decoration-circle circle-1 float"></view>
      <view class="decoration-circle circle-2 float-slow"></view>
      <view class="decoration-circle circle-3 float"></view>
    </view>
  </view>

  <!-- 活动列表区域 -->
  <view class="content-section">
    <!-- 区块标题 -->
    <view class="section-header">
      <text class="section-title text-2xl font-bold">热门活动</text>
      <text class="section-subtitle text-sm text-secondary">发现身边的精彩运动</text>
    </view>

    <!-- 活动卡片列表 -->
    <view class="activity-grid">
      <view class="activity-card {{getSportTheme(item.image)}} card-slide-up interactive"
            wx:for="{{activities}}"
            wx:key="id"
            bind:tap="onActivityTap"
            data-id="{{item.id}}"
            style="animation-delay: {{index * 100}}ms;">

        <!-- 活动头图 -->
        <view class="activity-card-image">
          <text class="sport-icon">{{item.image}}</text>
          <view class="sport-tag {{getTagClass(item.tagType)}}" wx:if="{{item.tag}}">
            {{item.tag}}
          </view>
          <!-- 渐变遮罩 -->
          <view class="image-overlay"></view>
        </view>

        <!-- 活动内容 -->
        <view class="activity-card-content">
          <text class="sport-card-title">{{item.title}}</text>

          <view class="sport-card-meta">
            <view class="sport-meta-item">
              <text class="sport-meta-icon">📅</text>
              <text class="meta-text">{{item.time}}</text>
            </view>
            <view class="sport-meta-item">
              <text class="sport-meta-icon">📍</text>
              <text class="meta-text">{{item.location}}</text>
            </view>
            <view class="sport-meta-item">
              <text class="sport-meta-icon">👥</text>
              <text class="meta-text">{{item.participants}}/{{item.maxParticipants}}人</text>
            </view>
          </view>

          <!-- 底部操作区 -->
          <view class="activity-footer">
            <view class="price-section">
              <text class="price-amount text-xl font-bold">¥{{item.price}}</text>
              <text class="price-unit text-xs text-secondary">人均</text>
            </view>
            <button class="btn btn-primary btn-sm" catchtap="onJoinActivity" data-id="{{item.id}}">
              立即参与
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多提示 -->
    <view class="load-more-section">
      <view class="loading-dots" wx:if="{{loading}}">
        <view class="dot"></view>
        <view class="dot"></view>
        <view class="dot"></view>
      </view>
      <text class="load-more-text text-sm text-tertiary" wx:else>上拉加载更多精彩活动</text>
    </view>
  </view>

  <!-- 悬浮发布按钮 -->
  <button class="fab fab-pulse bg-gradient-secondary" bind:tap="onPublishTap">
    <text class="fab-icon">+</text>
  </button>

  <!-- 城市选择器弹窗 -->
  <view class="city-modal {{showCitySelector ? 'show' : ''}}" bind:tap="onCloseCitySelector">
    <view class="city-modal-content slide-in-up" catchtap="stopPropagation">
      <!-- 弹窗头部 -->
      <view class="modal-header">
        <text class="modal-title text-xl font-bold">选择城市</text>
        <button class="btn btn-circle btn-ghost" bind:tap="onCloseCitySelector">
          <text>✕</text>
        </button>
      </view>

      <!-- 城市网格 -->
      <view class="city-grid">
        <view class="city-item {{currentCity === item ? 'selected' : ''}} interactive"
              wx:for="{{cities}}"
              wx:key="*this"
              bind:tap="onCitySelect"
              data-city="{{item}}">
          <text class="city-name">{{item}}</text>
          <view class="city-check" wx:if="{{currentCity === item}}">✓</view>
        </view>
      </view>
    </view>
  </view>
</view>