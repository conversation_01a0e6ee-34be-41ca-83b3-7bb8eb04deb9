/* pages/home/<USER>/
.home-container {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
  overflow-x: hidden;
}

/* 顶部区域 */
.top-section {
  width: 100%;
  background-color: #fff;
  padding: 24rpx 24rpx;
  border-bottom: 1rpx solid #e7e7e7;
  box-sizing: border-box;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.city-selector {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background-color: #f3f3f3;
}

.location-icon {
  font-size: 32rpx;
}

.city-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #262626;
}

.arrow-down {
  font-size: 24rpx;
  color: #8a8a8a;
}

.search-section {
  margin-top: 16rpx;
}

.search-input {
  background-color: #f3f3f3;
  border-radius: 24rpx;
}

/* 活动列表 */
.activity-list {
  width: 100%;
  padding: 24rpx;
  box-sizing: border-box;
}

.activity-item {
  background-color: #fff;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
}

.activity-item:active {
  transform: scale(0.98);
}

.activity-image {
  height: 160rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.sport-icon {
  font-size: 80rpx;
}

.activity-tag {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #fff;
  font-weight: 500;
}

.activity-tag.hot {
  background-color: #ff4757;
}

.activity-tag.new {
  background-color: #2ed573;
}

.activity-tag.limited {
  background-color: #ffa502;
}

.activity-info {
  padding: 32rpx;
}

.activity-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #262626;
  margin-bottom: 24rpx;
}

.activity-meta {
  margin-bottom: 24rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 12rpx;
  font-size: 28rpx;
  color: #8a8a8a;
}

.activity-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 底部内容区域 */
.bottom-content {
  padding: 40rpx 24rpx 120rpx;
  text-align: center;
}

.load-more-hint {
  font-size: 28rpx;
  color: #8a8a8a;
  padding: 20rpx;
}

.participant-info {
  font-size: 28rpx;
  color: #0052d9;
  background-color: #f2f6ff;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
}

.price-info {
  text-align: right;
}

.price {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff6b35;
}

.price-unit {
  font-size: 24rpx;
  color: #8a8a8a;
  margin-left: 8rpx;
}

/* 悬浮按钮 */
.fab-container {
  position: fixed;
  bottom: 120rpx;
  right: 24rpx;
  z-index: 100;
}

.fab-button {
  width: 112rpx !important;
  height: 112rpx !important;
  border-radius: 56rpx !important;
  box-shadow: 0 8rpx 24rpx rgba(0, 82, 217, 0.3);
}

/* 城市选择器弹窗 */
.city-popup {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 32rpx;
  max-height: 60vh;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid #e7e7e7;
}

.popup-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #262626;
}

.city-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
}

.city-item {
  padding: 24rpx;
  text-align: center;
  border-radius: 16rpx;
  background-color: #f8f8f8;
  font-size: 32rpx;
  color: #262626;
  transition: all 0.2s ease;
}

.city-item:active {
  background-color: #e7e7e7;
}

.city-item .selected {
  color: #0052d9;
  font-weight: 600;
}