/* pages/home/<USER>/
@import "../../styles/design-system.wxss";
@import "../../styles/components.wxss";
@import "../../styles/animations.wxss";
@import "../../styles/sports-theme.wxss";

/* ========================================
   页面容器
   ======================================== */

.page-container {
  min-height: 100vh;
  background: var(--bg-primary);
  position: relative;
  overflow-x: hidden;
}

/* ========================================
   英雄区域 (Hero Section)
   ======================================== */

.hero-section {
  position: relative;
  padding: var(--space-xl) var(--space-lg) var(--space-4xl);
  background: var(--primary-gradient);
  overflow: hidden;
}

.hero-content {
  position: relative;
  z-index: 2;
}

.top-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2xl);
}

.city-selector {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-full);
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  transition: all var(--duration-normal) var(--easing-ease);
}

.city-selector:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.98);
}

.location-icon {
  font-size: var(--font-base);
}

.city-name {
  font-size: var(--font-base);
  margin: 0 var(--space-xs);
}

.arrow-icon {
  font-size: var(--font-sm);
  transition: transform var(--duration-normal) var(--easing-ease);
}

.hero-title {
  text-align: center;
  margin-bottom: var(--space-2xl);
}

.title-main {
  display: block;
  font-size: var(--font-3xl);
  margin-bottom: var(--space-sm);
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.title-sub {
  display: block;
  font-size: var(--font-base);
  opacity: 0.9;
}

.search-container {
  margin-bottom: var(--space-lg);
}

.search-box {
  display: flex;
  align-items: center;
  padding: var(--space-md) var(--space-lg);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  transition: all var(--duration-normal) var(--easing-ease);
}

.search-box:focus-within {
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-4rpx);
  box-shadow: var(--shadow-lg);
}

.search-icon {
  font-size: var(--font-lg);
  color: var(--text-secondary);
  margin-right: var(--space-md);
}

.search-input {
  flex: 1;
  font-size: var(--font-base);
  color: var(--text-primary);
  background: transparent;
  border: none;
  outline: none;
}

.search-input::placeholder {
  color: var(--text-tertiary);
}

/* ========================================
   装饰性元素
   ======================================== */

.hero-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  border-radius: var(--radius-full);
  background: rgba(255, 255, 255, 0.1);
}

.circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  right: -50rpx;
}

.circle-2 {
  width: 120rpx;
  height: 120rpx;
  top: 60%;
  left: -30rpx;
}

.circle-3 {
  width: 80rpx;
  height: 80rpx;
  top: 30%;
  left: 20%;
}

/* ========================================
   内容区域
   ======================================== */

.content-section {
  padding: var(--space-xl) var(--space-lg);
  background: var(--bg-primary);
  margin-top: -40rpx;
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
  position: relative;
  z-index: 3;
}

.section-header {
  text-align: center;
  margin-bottom: var(--space-2xl);
}

.section-title {
  display: block;
  margin-bottom: var(--space-xs);
}

.section-subtitle {
  display: block;
}

/* ========================================
   活动卡片网格
   ======================================== */

.activity-grid {
  display: flex;
  flex-direction: column;
  gap: var(--space-xl);
}

.activity-card {
  background: var(--bg-secondary);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all var(--duration-normal) var(--easing-ease);
  position: relative;
}

.activity-card:active {
  transform: translateY(-8rpx);
  box-shadow: var(--shadow-xl);
}

.activity-card-image {
  height: 320rpx;
  background: var(--sport-gradient, var(--primary-gradient));
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.activity-card-image::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: sportCardGlow 4s ease-in-out infinite alternate;
}

.sport-icon {
  font-size: 120rpx;
  z-index: 2;
  position: relative;
  animation: float 3s ease-in-out infinite;
}

.sport-tag {
  position: absolute;
  top: var(--space-md);
  right: var(--space-md);
  z-index: 3;
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60rpx;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.1));
}

/* ========================================
   活动卡片内容
   ======================================== */

.activity-card-content {
  padding: var(--space-xl);
}

.sport-card-title {
  font-size: var(--font-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
  line-height: var(--leading-tight);
}

.sport-card-meta {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
  margin-bottom: var(--space-xl);
}

.sport-meta-item {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.sport-meta-icon {
  font-size: var(--font-base);
  width: 32rpx;
  text-align: center;
}

.meta-text {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  flex: 1;
}

.activity-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-section {
  display: flex;
  align-items: baseline;
  gap: var(--space-xs);
}

.price-amount {
  color: var(--color-primary);
}

.price-unit {
  color: var(--text-tertiary);
}

/* ========================================
   加载更多区域
   ======================================== */

.load-more-section {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--space-2xl) 0;
  margin-top: var(--space-xl);
}

.load-more-text {
  text-align: center;
}

/* ========================================
   城市选择模态框
   ======================================== */

.city-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: var(--z-modal);
  display: flex;
  align-items: flex-end;
  opacity: 0;
  visibility: hidden;
  transition: all var(--duration-normal) var(--easing-ease);
}

.city-modal.show {
  opacity: 1;
  visibility: visible;
}

.city-modal-content {
  width: 100%;
  background: var(--bg-secondary);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
  padding: var(--space-xl) var(--space-lg) var(--space-2xl);
  max-height: 80vh;
  transform: translateY(100%);
  transition: transform var(--duration-normal) var(--easing-ease);
}

.city-modal.show .city-modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2xl);
  padding-bottom: var(--space-lg);
  border-bottom: 1rpx solid var(--border-light);
}

.modal-title {
  color: var(--text-primary);
}

.city-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-lg);
}

.city-item {
  position: relative;
  padding: var(--space-lg);
  text-align: center;
  border-radius: var(--radius-lg);
  background: var(--bg-primary);
  border: 2rpx solid transparent;
  transition: all var(--duration-normal) var(--easing-ease);
}

.city-item:active {
  transform: scale(0.98);
}

.city-item.selected {
  background: var(--primary-gradient);
  color: var(--text-inverse);
  box-shadow: var(--shadow-md);
}

.city-name {
  font-size: var(--font-base);
  font-weight: var(--font-medium);
}

.city-check {
  position: absolute;
  top: var(--space-xs);
  right: var(--space-xs);
  font-size: var(--font-sm);
  color: var(--text-inverse);
}

/* ========================================
   工具函数样式
   ======================================== */

.fab-icon {
  font-size: var(--font-2xl);
  font-weight: var(--font-bold);
}