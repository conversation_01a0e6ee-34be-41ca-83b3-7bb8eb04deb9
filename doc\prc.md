《约球》小程序产品需求文档 (PRD) v1.5
一、概述与核心理念
1.1 项目概述
本小程序旨在打造一个高品质、可信赖的运动社交平台。它不仅帮助用户便捷地发现、发布和参与线下体育活动（如网球、羽毛球、篮球、足球等），更致力于通过智能匹配和信任机制，解决用户在约球过程中遇到的**“水平不匹配”和“行为不可靠”**两大核心痛点，最终构建一个充满活力与信任的运动社群。

1.2 设计原则
信任优先： 所有功能设计都应服务于建立用户间的信任感，将信用和水平作为核心要素贯穿产品。

效率至上： 简化用户操作路径，通过自动化和智能化手段降低组织和参与活动的沟通成本。

体验为王： 确保界面清晰、交互流畅，为用户提供愉悦、无缝的使用体验。

二、信息架构 (IA) 优化建议
为了提升用户体验和功能扩展性，建议将底部导航栏调整为以下结构：

导航Tab	核心功能	对应版本
首页	浏览和发现附近的活动	基础版
发现	聚合搜索功能：找活动、找教练、找场地	标准版
发布	快速发起活动的入口（悬浮按钮）	基础版
消息	聚合系统通知、活动通知、临时群聊	标准版
我的	个人运动档案、活动管理、资产中心	基础版

导出到 Google 表格
三、页面及功能详细说明
1. 首页 (Home)
页面目标
让用户快速发现身边感兴趣的高质量活动。

页面元素 (修正后)
顶部区域：

城市切换： 左上角显示当前城市，点击可手动选择。首次进入时请求地理位置授权，授权后自动定位 。   

搜索框： 居中，支持按关键词（如活动名、场地名）搜索。

筛选入口： 右上角图标，点击弹出筛选面板。

筛选功能 (增强)：

基础筛选： 运动类型、时间范围、区域。

高级筛选 (标准版)： 发起人信用分、活动平均水平要求、性别限制。

活动列表：

以卡片流形式展示，下拉刷新，上滑加载更多。

卡片信息优化：

活动封面图。

标题：清晰说明运动类型和主题，如“【羽毛球】周六下午欢乐双打局”。

核心标签 (增强)：

状态标签：🔥热门、🆕新发起、⚡️仅剩1位。

水平标签 (核心)： 新手友好、进阶切磋、高手过招。

时间、地点（显示距离，如“1.5km”）。

人数：已报名 5 / 8。

发起人信息 (核心)： 头像、昵称、信用分 (如“信用: 优”)。

费用：¥50/人 或 AA制。

交互逻辑
点击任意卡片，跳转至【活动详情页】。

未授权定位时，默认展示热门城市（如上海）的活动，并有明显提示引导用户授权。

2. 活动详情页 (Activity Detail)
页面目标
全面展示活动信息，建立参与者对活动的信任感，并提供清晰的操作指引。

页面元素 (重构)
顶部核心信息：

活动标题、活动状态（报名中/已满员/已结束/已取消）。

时间、地点（含导航入口）、费用。

发起人信息模块 (核心)：

展示发起人头像、昵称、综合能力分和信用分。

点击头像可进入其【个人主页】。

活动要求模块 (核心)：

水平要求： 如“欢迎能力分在 2.0-3.5 的球友”。

信用要求： 如“需要信用分良好及以上”。

其他备注：如“请自备球拍，场地提供饮水”。

已报名用户列表：

横向滚动展示已确认参与者的头像，点击可查看其个人主页。

显示“X人已报名，Y人已确认”。

场地照片/介绍： 可展示多张场地实拍图。

临时活动群聊 (标准版)：

逻辑： 用户报名并被发起人确认后，此模块才可见。

功能： 点击进入一个与本次活动绑定的临时聊天室，方便赛前沟通。活动结束后自动归档 。   

底部操作栏 (动态变化)：

对于普通用户：

未报名：显示收藏和立即报名按钮。

已报名待确认：取消申请按钮。

已确认：进入群聊按钮。

满员/结束：报名已截止（置灰）。

对于发起人： 显示分享和管理活动按钮。点击“管理活动”进入【活动管理页】。

3. 个人中心 (My Profile) - 原“我的页面”
页面目标
打造用户的“运动身份名片”，并提供个人相关的管理功能。

页面元素 (重构)
顶部个人信息卡片 (核心)：

用户头像、昵称。

综合能力分 (核心)： 一个基于AI评估和赛后互评的动态分数，是用户运动水平的直观体现 。旁边有“？”图标，点击可查看分数说明。   

信用分 (核心)： 基于出勤、守时、无故取消等行为计算。

关注/粉丝数。

核心数据统计：

累计参与活动次数、累计运动时长等。

我的资产/服务：

入口：我的钱包、我的教练、我的收藏（活动/场地/教练）。

活动管理Tabs (保留并优化)：

我发起的： 管理自己创建的活动。

我参与的： 展示所有已确认参加的活动（进行中/待开始/待评价/已完成）。

我申请的： 跟踪申请状态（待审核/已拒绝）。

其他入口：

设置（账号与安全、通知设置等）、帮助与反馈、联系客服。

4. 发布活动页 (Create Activity)
页面目标
引导用户快速、清晰地创建一个有吸引力的活动。

页面元素 (增强)
基础信息： 活动标题、运动类型、时间、人数、费用模式。

场地选择：

支持从“我的收藏”或“附近场地”中选择。

支持手动输入地址。

门槛设置 (核心)：

水平要求： 可设置一个能力分范围，如“2.5 - 4.0”。

信用要求： 可选择“不限”、“良好及以上”或“优秀”。

活动描述： 详细介绍，支持图文。

封面图上传。

5. 新增核心页面与流程
5.1 发现页 (Discover) - (标准版)
页面目标： 成为用户寻找运动相关资源的总入口。

页面结构： 顶部为Tabs，切换不同内容。

找活动Tab： 即增强版的首页，提供更丰富的筛选和排序。

找教练Tab：

以列表或卡片形式展示平台认证的教练。

卡片信息：教练头像、主攻项目、教龄、评分、价格。

支持按项目、区域、价格筛选。

点击进入【教练详情页】，可查看其详细介绍、学员评价、可预约时间，并进行在线预约。

找场地Tab：

功能同原PRD中的【预订场地页面】，但作为“发现”的一部分，逻辑更清晰。

集成地图服务 ，提供场地列表和地图两种视图模式。   

详情页增加用户评论、评分、设施标签（淋浴/停车等）。

5.2 消息中心 (Message Center) - (标准版)
页面目标： 统一处理所有通知，避免信息干扰。

页面结构：

系统通知： 官方公告、版本更新等。

互动通知： 谁申请了你的活动、申请被通过/拒绝、活动被取消等。

活动群聊： 所有临时活动群聊的入口列表。

5.3 活动后评价流程 (Post-Activity Rating Flow) - (标准版)
触发时机： 活动预设的结束时间到达后，系统自动向所有“已确认”参与者推送评价通知。

评价内容：

对发起人： 评价组织能力、场地情况、氛围等（5星制+标签）。

对其他参与者 (匿名互评)：

水平评价： “比我预估的高”、“符合预期”、“比我预估的低”。此项数据将作为AI能力分模型的重要输入 。   

行为评价： 评价对方是否“守时友好”、“积极拼搏”等。

四、技术方案建议 (修正后)
前端框架： 优先选用微信小程序原生开发，以获得最佳性能和最完整的微信生态能力支持（如微信支付、订阅消息）。   

后端方案：

启动期 (基础版/标准版)： 强烈建议采用微信云开发。优势在于开发速度快、初期成本低、免运维，能让我们聚焦于业务逻辑的快速迭代 。   

成熟期 (旗舰版)： 可根据业务发展，平滑迁移至自建后端（如 Node.js + MongoDB/PostgreSQL），以获得完全的数据控制权和更高的定制化能力 。   

地图服务： 集成腾讯位置服务LBS小程序插件，快速实现定位、路线规划、地点搜索等功能 。   

数据交互： 采用标准的 RESTful API，数据格式为 JSON。

消息推送： 严格使用微信一次性订阅消息机制，在用户主动操作（如报名、预约）后引导其订阅，确保通知的合规与高效触达 。   

五、版本规划 (修正后)
此规划与总方案对齐，确保开发资源聚焦于每个阶段的核心目标。

V1.0 基础版 (MVP)
目标： 验证核心“约球”流程，跑通商业模式。

核心功能：

首页（基础版）、活动详情页（基础版）、发布活动页。

个人中心（含基础的自评式水平/信用展示）。

微信授权登录、在线支付报名。

不包含： 找教练、找场地、临时群聊、复杂的AI评分。

V2.0 标准版
目标： 构建信任体系，提升用户留存，探索商业化。

核心功能 (在基础版上新增)：

信任体系： 引入赛后互评机制、动态信用分。

服务生态： 上线“发现”页，包含“找教练”和“找场地”功能。

社交通讯： 上线“消息中心”和“临时活动群聊”。

体验优化： 更高级的筛选和排序功能。

V3.0 旗舰版
目标： 建立技术壁垒，打造行业领导地位。

核心功能 (在标准版上新增)：

智能匹配： 引入基于Elo算法的动态AI能力分模型，实现精准匹配 。   

社群运营： 增加“运动圈”等社交动态功能，引入勋章、排行榜等游戏化体系。

数据增值： 探索与智能穿戴设备的数据打通，提供个人运动数据分析报告。