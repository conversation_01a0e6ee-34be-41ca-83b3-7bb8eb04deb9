# 约球小程序前端原型说明（PRD）

## 一、概述

本小程序旨在为用户提供便捷的线下运动约球平台，帮助用户发现、发布和参与各类体育活动（如网球、羽毛球、篮球、足球等），实现社交与运动结合的体验。

---

## 二、页面及功能说明

### 1. 首页

#### 页面名称
首页

#### 页面样子
- 活动以“卡片形式”展示，卡片信息包含：活动名称、时间、地点、活动封面图、已报名人数。
- 页面顶部显示城市名称，点击可切换城市。
- 若无授权定位，默认城市为“上海”。

#### 页面功能
- 请求用户地理位置，用于展示对应城市的活动信息。
- 支持手动选择城市切换活动列表。
- 下拉刷新 / 自动加载更多。
- 每张卡片点击可跳转到活动详细页面。

#### 补充功能建议
- 活动卡片可增加标签（如“🔥热门”“🆕新活动”“仅剩2位”等）。
- 提供“筛选”入口，筛选条件如：活动类型、时间、性别限制等。

---

### 2. 活动详细页面

#### 页面名称
活动详情页

#### 页面样子
- 顶部展示活动介绍（标题、描述、时间、地点、主办人信息）。
- 中部展示场地照片（1张大图）。
- 下方列表展示已被允许参与的人员头像与昵称。
- 页面右下角悬浮两个操作按钮：“预订场地”“申请参与活动”。

#### 页面功能
- 展示活动详情信息：
  - 发布者头像与昵称
  - 活动时间、场地名称与地址
  - 当前参与人数 / 限制人数
  - 活动介绍文字
  - 场地图片
  - 已确认参与人员头像昵称
- 点击“预订场地”跳转至【预订场地】页面
- 点击“申请参与活动”跳转至【申请参与活动】页面
- 页面支持分享和收藏功能

#### 补充交互建议
- 若当前用户是活动发布者，则隐藏“申请参与活动”按钮，显示“管理活动”按钮。
- 若活动已截止或人数满，则“申请参与”活动按钮变灰不可点击。

---

### 3. 我的页面

#### 页面名称
我的

#### 页面样子
- 页面顶部展示用户头像、昵称、手机号（或微信授权昵称）
- 下方为 3 个 Tab：
  - 我发布的
  - 我申请的
  - 我参与的
- 每个 Tab 下为活动列表项：展示活动名称、时间、地点、状态

#### 页面功能
- 展示用户相关的活动信息和状态
- 可执行以下操作：
  - “我发布的”：可点击进入详情、修改、取消发布（带确认弹窗）
  - “我申请的”：支持撤回申请（带确认弹窗）
  - “我参与的”：显示状态（已确认 / 待确认 / 已结束）
- 支持列表分页 / 下拉刷新

#### 补充建议
- 提供“设置”按钮可查看绑定手机号、更改昵称等。
- 可增加消息通知提醒用户申请结果或活动变更。

---

### 4. 预订场地页面

#### 页面名称
预订场地

#### 页面样子
- 顶部为筛选条件：
  - 城市（默认当前定位城市）
  - 活动类型（网球/羽毛球/篮球等）
  - 价格区间
  - 距离
- 中部为场地列表：
  - 每项展示：场地封面图 + 名称 + 距离 + 人均价格 + 标签（如“推荐”）
- 页面底部或右下角提供地图展示按钮
- 地图组件展示附近球场位置，可点击进入详情页

#### 页面功能
- 用户可按筛选条件查找球场
- 场地列表支持点击查看详细信息（价格、可预约时间段等）
- 用户可选择日期、时间段、人数进行预约
- 预约成功后生成记录，显示在“我的”页面中

#### 补充建议
- 场地详情页可支持评论和评分系统
- 可展示场地是否支持淋浴、停车、租借器材等额外设施信息

---

### 5. 申请参与活动页面

#### 页面名称
申请参与活动

#### 页面样子
- 展示活动介绍信息（复用活动详情页的部分信息）
- 显示发布者信息、场地图、活动时间等

#### 页面功能
- 提交“申请参与”活动的请求
- 可填写备注（如擅长水平、希望组队）
- 提交后跳转提示“申请已提交，等待确认”，同时更新“我申请的”列表状态

#### 补充建议
- 防止重复提交申请
- 若活动设置了性别/水平要求，需进行校验提示

---

## 三、通用功能建议

- 所有页面支持夜间模式切换
- 使用微信开放能力实现：用户登录、微信头像昵称授权、位置授权、订阅消息推送等
- 提供全局 loading、空状态页和错误提示处理
- 所有关键操作建议加入埋点和统计，为后期分析优化提供数据支撑

---

## 四、技术建议

- 前端框架：使用微信原生小程序或 uni-app 开发
- 地图服务：可集成腾讯位置服务或高德地图小程序 SDK
- 用户信息存储：建议结合微信云开发的云函数 + 云数据库
- 消息推送：结合微信订阅消息提醒活动申请结果、预约提醒等

---

## 五、版本控制

- 当前版本：v1.0 原型功能说明
- 下个版本目标：
  - 增加活动评论、评分系统
  - 支持队伍组建与匹配算法
  - 小程序桌面图标活动提醒能力（如 badge 点）

---

