/**
 * 约球小程序 - 运动主题样式
 * Sports Theme Styles
 */

/* ========================================
   运动类型主题色彩
   ======================================== */

.sport-badminton {
  --sport-color: #667eea;
  --sport-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --sport-light: rgba(102, 126, 234, 0.1);
}

.sport-basketball {
  --sport-color: #f5576c;
  --sport-gradient: linear-gradient(135deg, #f5576c 0%, #f093fb 100%);
  --sport-light: rgba(245, 87, 108, 0.1);
}

.sport-tennis {
  --sport-color: #4facfe;
  --sport-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --sport-light: rgba(79, 172, 254, 0.1);
}

.sport-football {
  --sport-color: #2ed573;
  --sport-gradient: linear-gradient(135deg, #2ed573 0%, #7bed9f 100%);
  --sport-light: rgba(46, 213, 115, 0.1);
}

.sport-pingpong {
  --sport-color: #ffa502;
  --sport-gradient: linear-gradient(135deg, #ffa502 0%, #ff6348 100%);
  --sport-light: rgba(255, 165, 2, 0.1);
}

.sport-running {
  --sport-color: #ff6b6b;
  --sport-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  --sport-light: rgba(255, 107, 107, 0.1);
}

.sport-swimming {
  --sport-color: #74b9ff;
  --sport-gradient: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  --sport-light: rgba(116, 185, 255, 0.1);
}

.sport-cycling {
  --sport-color: #a29bfe;
  --sport-gradient: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
  --sport-light: rgba(162, 155, 254, 0.1);
}

/* ========================================
   运动卡片样式
   ======================================== */

.sport-card {
  background: var(--bg-secondary);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all var(--duration-normal) var(--easing-ease);
  position: relative;
}

.sport-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 8rpx;
  background: var(--sport-gradient);
}

.sport-card:active {
  transform: translateY(-8rpx);
  box-shadow: var(--shadow-lg);
}

.sport-card-header {
  height: 320rpx;
  background: var(--sport-gradient);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.sport-card-header::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: sportCardGlow 3s ease-in-out infinite alternate;
}

@keyframes sportCardGlow {
  0% {
    transform: rotate(0deg) scale(1);
  }
  100% {
    transform: rotate(180deg) scale(1.1);
  }
}

.sport-icon {
  font-size: 120rpx;
  z-index: 1;
  position: relative;
  animation: float 3s ease-in-out infinite;
}

.sport-card-content {
  padding: var(--space-xl);
}

.sport-card-title {
  font-size: var(--font-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-md);
}

.sport-card-meta {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
  margin-bottom: var(--space-lg);
}

.sport-meta-item {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  font-size: var(--font-sm);
  color: var(--text-secondary);
}

.sport-meta-icon {
  font-size: var(--font-base);
  color: var(--sport-color);
}

/* ========================================
   运动标签系统
   ======================================== */

.sport-tag {
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-xs) var(--space-md);
  border-radius: var(--radius-full);
  font-size: var(--font-xs);
  font-weight: var(--font-medium);
  background: var(--sport-light);
  color: var(--sport-color);
  border: 1rpx solid var(--sport-color);
}

.sport-tag-gradient {
  background: var(--sport-gradient);
  color: var(--text-inverse);
  border: none;
}

.sport-tag-hot {
  background: var(--danger-gradient);
  color: var(--text-inverse);
  animation: pulse 2s infinite;
}

.sport-tag-new {
  background: var(--success-gradient);
  color: var(--text-inverse);
  position: relative;
  overflow: hidden;
}

.sport-tag-new::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: tagShine 2s infinite;
}

@keyframes tagShine {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.sport-tag-limited {
  background: var(--warning-gradient);
  color: var(--text-inverse);
  animation: blink 1s infinite;
}

/* ========================================
   运动统计样式
   ======================================== */

.sport-stats {
  display: flex;
  justify-content: space-around;
  padding: var(--space-lg);
  background: var(--sport-light);
  border-radius: var(--radius-lg);
  margin: var(--space-lg) 0;
}

.sport-stat-item {
  text-align: center;
}

.sport-stat-number {
  font-size: var(--font-2xl);
  font-weight: var(--font-bold);
  color: var(--sport-color);
  display: block;
  margin-bottom: var(--space-xs);
}

.sport-stat-label {
  font-size: var(--font-xs);
  color: var(--text-secondary);
}

/* ========================================
   运动进度条
   ======================================== */

.sport-progress {
  width: 100%;
  height: 12rpx;
  background: var(--neutral-200);
  border-radius: var(--radius-full);
  overflow: hidden;
  position: relative;
}

.sport-progress-bar {
  height: 100%;
  background: var(--sport-gradient);
  border-radius: var(--radius-full);
  transition: width var(--duration-slow) var(--easing-ease);
  position: relative;
  overflow: hidden;
}

.sport-progress-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progressShine 2s infinite;
}

@keyframes progressShine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* ========================================
   运动等级徽章
   ======================================== */

.sport-level-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-full);
  background: var(--sport-gradient);
  color: var(--text-inverse);
  font-size: var(--font-sm);
  font-weight: var(--font-bold);
  position: relative;
  overflow: hidden;
}

.sport-level-badge::before {
  content: '';
  position: absolute;
  top: -2rpx;
  left: -2rpx;
  right: -2rpx;
  bottom: -2rpx;
  background: var(--sport-gradient);
  border-radius: var(--radius-full);
  z-index: -1;
  animation: levelBadgeGlow 2s ease-in-out infinite alternate;
}

@keyframes levelBadgeGlow {
  0% {
    filter: blur(0);
  }
  100% {
    filter: blur(4rpx);
  }
}

.sport-level-icon {
  font-size: var(--font-base);
}

/* ========================================
   运动场地样式
   ======================================== */

.venue-card {
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: all var(--duration-normal) var(--easing-ease);
  border-left: 8rpx solid var(--sport-color);
}

.venue-card:active {
  transform: translateY(-4rpx);
  box-shadow: var(--shadow-md);
}

.venue-rating {
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  background: var(--sport-light);
  color: var(--sport-color);
  font-size: var(--font-xs);
  font-weight: var(--font-medium);
}

.venue-rating-stars {
  color: #fbbf24;
}

/* ========================================
   运动时间轴
   ======================================== */

.sport-timeline {
  position: relative;
  padding-left: var(--space-xl);
}

.sport-timeline::before {
  content: '';
  position: absolute;
  left: var(--space-md);
  top: 0;
  bottom: 0;
  width: 4rpx;
  background: var(--sport-gradient);
  border-radius: var(--radius-full);
}

.sport-timeline-item {
  position: relative;
  margin-bottom: var(--space-xl);
}

.sport-timeline-item::before {
  content: '';
  position: absolute;
  left: -28rpx;
  top: var(--space-sm);
  width: 24rpx;
  height: 24rpx;
  border-radius: var(--radius-full);
  background: var(--sport-color);
  border: 4rpx solid var(--bg-secondary);
  box-shadow: 0 0 0 4rpx var(--sport-light);
}

.sport-timeline-content {
  background: var(--bg-secondary);
  padding: var(--space-lg);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

/* ========================================
   运动成就样式
   ======================================== */

.sport-achievement {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-lg);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--duration-normal) var(--easing-ease);
  position: relative;
  overflow: hidden;
}

.sport-achievement::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: var(--sport-gradient);
}

.sport-achievement:active {
  transform: scale(0.98);
}

.sport-achievement-icon {
  width: 96rpx;
  height: 96rpx;
  border-radius: var(--radius-full);
  background: var(--sport-gradient);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-xl);
  color: var(--text-inverse);
  flex-shrink: 0;
}

.sport-achievement-content {
  flex: 1;
}

.sport-achievement-title {
  font-size: var(--font-lg);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.sport-achievement-desc {
  font-size: var(--font-sm);
  color: var(--text-secondary);
}

.sport-achievement-points {
  font-size: var(--font-sm);
  font-weight: var(--font-bold);
  color: var(--sport-color);
  background: var(--sport-light);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
}
