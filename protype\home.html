<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .activity-card {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            margin-bottom: 12px;
            overflow: hidden;
            transition: all 0.2s;
            cursor: pointer;
        }
        
        .activity-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.12);
        }
        
        .activity-image {
            width: 100%;
            height: 120px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }
        
        .activity-tag {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(255,255,255,0.9);
            color: #ff4757;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .city-selector {
            background: #f8f9fa;
            border-radius: 20px;
            padding: 6px 12px;
            display: inline-flex;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .city-selector:hover {
            background: #e9ecef;
        }
        
        .filter-btn {
            background: #0052d9;
            color: white;
            border: none;
            border-radius: 20px;
            padding: 6px 16px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .filter-btn:hover {
            background: #003db8;
        }
        
        .participant-count {
            background: #f2f3ff;
            color: #0052d9;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="h-full overflow-y-auto">
        <!-- 顶部区域 -->
        <div class="bg-white px-4 py-3 border-b border-gray-100">
            <div class="flex items-center justify-between mb-3">
                <div class="city-selector" onclick="showCitySelector()">
                    <span class="text-lg">📍</span>
                    <span class="font-medium">上海</span>
                    <span class="text-gray-400">▼</span>
                </div>
                <button class="filter-btn" onclick="showFilter()">
                    筛选
                </button>
            </div>
            
            <!-- 搜索框 -->
            <div class="relative">
                <input type="text" placeholder="搜索活动、场地..." 
                       class="w-full bg-gray-100 rounded-full px-4 py-2 pl-10 text-sm border-none outline-none">
                <span class="absolute left-3 top-2.5 text-gray-400">🔍</span>
            </div>
        </div>
        
        <!-- 活动列表 -->
        <div class="px-4 py-4">
            <!-- 活动卡片1 -->
            <div class="activity-card" onclick="openActivityDetail(1)">
                <div class="activity-image">
                    🏸
                    <div class="activity-tag">🔥 热门</div>
                </div>
                <div class="p-4">
                    <h3 class="font-semibold text-lg mb-2">周末羽毛球约战</h3>
                    <div class="flex items-center gap-2 text-gray-600 text-sm mb-2">
                        <span>⏰</span>
                        <span>今天 19:00-21:00</span>
                    </div>
                    <div class="flex items-center gap-2 text-gray-600 text-sm mb-3">
                        <span>📍</span>
                        <span>浦东新区羽毛球馆</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="participant-count">
                            已报名 6/8 人
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-orange-500">¥45</div>
                            <div class="text-xs text-gray-500">人均</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 活动卡片2 -->
            <div class="activity-card" onclick="openActivityDetail(2)">
                <div class="activity-image" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                    🏀
                    <div class="activity-tag">🆕 新活动</div>
                </div>
                <div class="p-4">
                    <h3 class="font-semibold text-lg mb-2">篮球3v3斗牛</h3>
                    <div class="flex items-center gap-2 text-gray-600 text-sm mb-2">
                        <span>⏰</span>
                        <span>明天 15:00-17:00</span>
                    </div>
                    <div class="flex items-center gap-2 text-gray-600 text-sm mb-3">
                        <span>📍</span>
                        <span>静安区体育中心</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="participant-count">
                            已报名 4/6 人
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-orange-500">¥30</div>
                            <div class="text-xs text-gray-500">人均</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 活动卡片3 -->
            <div class="activity-card" onclick="openActivityDetail(3)">
                <div class="activity-image" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                    🎾
                    <div class="activity-tag" style="background: rgba(255,193,7,0.9); color: #000;">仅剩2位</div>
                </div>
                <div class="p-4">
                    <h3 class="font-semibold text-lg mb-2">网球双打练习</h3>
                    <div class="flex items-center gap-2 text-gray-600 text-sm mb-2">
                        <span>⏰</span>
                        <span>后天 09:00-11:00</span>
                    </div>
                    <div class="flex items-center gap-2 text-gray-600 text-sm mb-3">
                        <span>📍</span>
                        <span>徐汇区网球俱乐部</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="participant-count">
                            已报名 6/8 人
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-orange-500">¥80</div>
                            <div class="text-xs text-gray-500">人均</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 活动卡片4 -->
            <div class="activity-card" onclick="openActivityDetail(4)">
                <div class="activity-image" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                    ⚽
                </div>
                <div class="p-4">
                    <h3 class="font-semibold text-lg mb-2">足球友谊赛</h3>
                    <div class="flex items-center gap-2 text-gray-600 text-sm mb-2">
                        <span>⏰</span>
                        <span>周六 16:00-18:00</span>
                    </div>
                    <div class="flex items-center gap-2 text-gray-600 text-sm mb-3">
                        <span>📍</span>
                        <span>黄浦区足球场</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="participant-count">
                            已报名 18/22 人
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-orange-500">¥25</div>
                            <div class="text-xs text-gray-500">人均</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 加载更多 -->
            <div class="text-center py-4">
                <button class="text-gray-500 text-sm" onclick="loadMore()">
                    加载更多活动...
                </button>
            </div>
        </div>
    </div>

    <script>
        function openActivityDetail(id) {
            // 在实际应用中，这里会跳转到活动详情页
            window.open(`activity-detail.html?id=${id}`, '_blank');
        }
        
        function showCitySelector() {
            alert('城市选择功能');
        }
        
        function showFilter() {
            alert('筛选功能');
        }
        
        function loadMore() {
            alert('加载更多活动');
        }
        
        // 模拟下拉刷新
        let startY = 0;
        let currentY = 0;
        let isRefreshing = false;
        
        document.addEventListener('touchstart', function(e) {
            startY = e.touches[0].clientY;
        });
        
        document.addEventListener('touchmove', function(e) {
            currentY = e.touches[0].clientY;
            if (currentY - startY > 100 && !isRefreshing && window.scrollY === 0) {
                isRefreshing = true;
                // 模拟刷新
                setTimeout(() => {
                    isRefreshing = false;
                    alert('刷新完成');
                }, 1000);
            }
        });
    </script>
</body>
</html>