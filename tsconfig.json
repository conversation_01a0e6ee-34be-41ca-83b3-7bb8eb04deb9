{"compilerOptions": {"target": "es6", "module": "commonjs", "lib": ["es6", "dom"], "moduleResolution": "node", "allowJs": true, "checkJs": false, "baseUrl": ".", "paths": {"@utils/*": ["utils/*"], "@pages/*": ["pages/*"], "tdesign-miniprogram/*": ["./miniprogram/miniprogram_npm/tdesign-miniprogram/*"]}, "outDir": "dist", "strict": true, "noImplicitAny": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true}, "include": ["**/*"], "exclude": ["node_modules", "miniprogram_npm"]}