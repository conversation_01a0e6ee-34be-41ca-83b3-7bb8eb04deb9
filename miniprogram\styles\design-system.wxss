/**
 * 约球小程序 - 活力运动设计系统
 * Design System for Sports Activity Mini Program
 * Version: 2.0
 */

/* ========================================
   设计令牌 (Design Tokens)
   ======================================== */

page {
  /* 主色调 - 活力渐变系列 */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  
  /* 运动主题色彩 */
  --sport-badminton: #667eea;
  --sport-basketball: #f5576c;
  --sport-tennis: #4facfe;
  --sport-football: #2ed573;
  --sport-pingpong: #ffa502;
  --sport-running: #ff6b6b;
  --sport-swimming: #74b9ff;
  --sport-cycling: #a29bfe;
  
  /* 中性色系 - 现代简约 */
  --neutral-50: #fafbfc;
  --neutral-100: #f4f6f8;
  --neutral-200: #e4e7eb;
  --neutral-300: #d1d5db;
  --neutral-400: #9ca3af;
  --neutral-500: #6b7280;
  --neutral-600: #4b5563;
  --neutral-700: #374151;
  --neutral-800: #1f2937;
  --neutral-900: #111827;
  
  /* 语义化颜色 */
  --color-primary: #667eea;
  --color-secondary: #f5576c;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-danger: #ef4444;
  --color-info: #3b82f6;
  
  /* 背景色系 */
  --bg-primary: var(--neutral-50);
  --bg-secondary: #ffffff;
  --bg-card: rgba(255, 255, 255, 0.95);
  --bg-overlay: rgba(0, 0, 0, 0.5);
  --bg-glass: rgba(255, 255, 255, 0.8);
  
  /* 文本色系 */
  --text-primary: var(--neutral-900);
  --text-secondary: var(--neutral-600);
  --text-tertiary: var(--neutral-400);
  --text-inverse: #ffffff;
  --text-link: var(--color-primary);
  
  /* 边框色系 */
  --border-light: var(--neutral-200);
  --border-medium: var(--neutral-300);
  --border-strong: var(--neutral-400);
  
  /* 阴影系统 */
  --shadow-xs: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  --shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
  --shadow-lg: 0 8rpx 32rpx rgba(0, 0, 0, 0.16);
  --shadow-xl: 0 16rpx 64rpx rgba(0, 0, 0, 0.20);
  
  /* 圆角系统 */
  --radius-xs: 8rpx;
  --radius-sm: 12rpx;
  --radius-md: 16rpx;
  --radius-lg: 24rpx;
  --radius-xl: 32rpx;
  --radius-full: 9999rpx;
  
  /* 间距系统 */
  --space-xs: 8rpx;
  --space-sm: 12rpx;
  --space-md: 16rpx;
  --space-lg: 24rpx;
  --space-xl: 32rpx;
  --space-2xl: 48rpx;
  --space-3xl: 64rpx;
  --space-4xl: 96rpx;
  
  /* 字体系统 */
  --font-family-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  
  /* 字体大小 */
  --font-xs: 24rpx;
  --font-sm: 28rpx;
  --font-base: 32rpx;
  --font-lg: 36rpx;
  --font-xl: 40rpx;
  --font-2xl: 48rpx;
  --font-3xl: 56rpx;
  --font-4xl: 64rpx;
  
  /* 字体粗细 */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  
  /* 行高系统 */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.75;
  
  /* 动画系统 */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
  
  --easing-ease: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --easing-ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  --easing-ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);
  --easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* Z-index 层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* ========================================
   基础重置样式
   ======================================== */

page {
  width: 100%;
  height: 100vh;
  background: var(--bg-primary);
  font-family: var(--font-family-primary);
  font-size: var(--font-base);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

view, text, image, button, input, textarea, scroll-view, swiper, swiper-item {
  box-sizing: border-box;
}

button {
  background: initial;
  border: none;
  outline: none;
  font-family: inherit;
}

button::after {
  border: none;
}

/* ========================================
   工具类 (Utility Classes)
   ======================================== */

/* 文本样式 */
.text-xs { font-size: var(--font-xs); }
.text-sm { font-size: var(--font-sm); }
.text-base { font-size: var(--font-base); }
.text-lg { font-size: var(--font-lg); }
.text-xl { font-size: var(--font-xl); }
.text-2xl { font-size: var(--font-2xl); }
.text-3xl { font-size: var(--font-3xl); }
.text-4xl { font-size: var(--font-4xl); }

.font-light { font-weight: var(--font-light); }
.font-normal { font-weight: var(--font-normal); }
.font-medium { font-weight: var(--font-medium); }
.font-semibold { font-weight: var(--font-semibold); }
.font-bold { font-weight: var(--font-bold); }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-inverse { color: var(--text-inverse); }

/* 间距工具 */
.p-xs { padding: var(--space-xs); }
.p-sm { padding: var(--space-sm); }
.p-md { padding: var(--space-md); }
.p-lg { padding: var(--space-lg); }
.p-xl { padding: var(--space-xl); }

.m-xs { margin: var(--space-xs); }
.m-sm { margin: var(--space-sm); }
.m-md { margin: var(--space-md); }
.m-lg { margin: var(--space-lg); }
.m-xl { margin: var(--space-xl); }

/* 圆角工具 */
.rounded-xs { border-radius: var(--radius-xs); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }

/* 阴影工具 */
.shadow-xs { box-shadow: var(--shadow-xs); }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* 背景工具 */
.bg-primary { background: var(--bg-primary); }
.bg-secondary { background: var(--bg-secondary); }
.bg-card { background: var(--bg-card); }
.bg-glass { 
  background: var(--bg-glass);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
}

/* 渐变背景 */
.bg-gradient-primary { background: var(--primary-gradient); }
.bg-gradient-secondary { background: var(--secondary-gradient); }
.bg-gradient-success { background: var(--success-gradient); }
.bg-gradient-warning { background: var(--warning-gradient); }
.bg-gradient-danger { background: var(--danger-gradient); }

/* 布局工具 */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.w-full { width: 100%; }
.h-full { height: 100%; }

/* 动画工具 */
.transition-all {
  transition: all var(--duration-normal) var(--easing-ease);
}

.transition-fast {
  transition: all var(--duration-fast) var(--easing-ease);
}

.transition-slow {
  transition: all var(--duration-slow) var(--easing-ease);
}

/* 交互状态 */
.interactive {
  transition: all var(--duration-fast) var(--easing-ease);
}

.interactive:active {
  transform: scale(0.98);
  opacity: 0.8;
}

/* 毛玻璃效果 */
.glass-effect {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}
