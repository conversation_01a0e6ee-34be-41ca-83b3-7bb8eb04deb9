# 约球小程序 - 组件使用示例

## 🎯 按钮组件示例

### 基础按钮
```html
<!-- WXML -->
<view class="button-examples">
  <!-- 主要按钮 -->
  <button class="btn btn-primary">立即参与</button>
  
  <!-- 次要按钮 -->
  <button class="btn btn-secondary">查看详情</button>
  
  <!-- 成功按钮 -->
  <button class="btn btn-success">确认报名</button>
  
  <!-- 轮廓按钮 -->
  <button class="btn btn-outline">取消</button>
  
  <!-- 幽灵按钮 -->
  <button class="btn btn-ghost">了解更多</button>
</view>
```

### 不同尺寸按钮
```html
<!-- WXML -->
<view class="button-sizes">
  <button class="btn btn-primary btn-xs">超小按钮</button>
  <button class="btn btn-primary btn-sm">小按钮</button>
  <button class="btn btn-primary">标准按钮</button>
  <button class="btn btn-primary btn-lg">大按钮</button>
  <button class="btn btn-primary btn-xl">超大按钮</button>
</view>
```

### 圆形按钮
```html
<!-- WXML -->
<view class="circle-buttons">
  <button class="btn btn-primary btn-circle">+</button>
  <button class="btn btn-secondary btn-circle btn-sm">❤️</button>
  <button class="btn btn-success btn-circle btn-lg">✓</button>
</view>
```

## 🃏 卡片组件示例

### 基础卡片
```html
<!-- WXML -->
<view class="card">
  <view class="card-header">
    <text class="text-lg font-bold">卡片标题</text>
  </view>
  <view class="card-body">
    <text class="text-base text-secondary">这是卡片的内容区域，可以放置任何内容。</text>
  </view>
  <view class="card-footer">
    <button class="btn btn-primary btn-sm">操作按钮</button>
  </view>
</view>
```

### 毛玻璃卡片
```html
<!-- WXML -->
<view class="card card-glass">
  <view class="card-body">
    <text class="text-lg font-bold">毛玻璃效果</text>
    <text class="text-sm text-secondary">半透明背景，现代感十足</text>
  </view>
</view>
```

### 渐变卡片
```html
<!-- WXML -->
<view class="card card-gradient">
  <view class="card-body">
    <text class="text-lg font-bold text-inverse">渐变卡片</text>
    <text class="text-sm text-inverse">使用主题渐变背景</text>
  </view>
</view>
```

## 🏃‍♂️ 运动卡片示例

### 活动卡片
```html
<!-- WXML -->
<view class="activity-card sport-badminton clickable" bindtap="onActivityTap">
  <view class="activity-card-image">
    <text class="sport-icon">🏸</text>
    <view class="sport-tag sport-tag-hot">🔥 热门</view>
  </view>
  <view class="activity-card-content">
    <text class="sport-card-title">周末羽毛球约战</text>
    <view class="sport-card-meta">
      <view class="sport-meta-item">
        <text class="sport-meta-icon">📅</text>
        <text>今天 19:00-21:00</text>
      </view>
      <view class="sport-meta-item">
        <text class="sport-meta-icon">📍</text>
        <text>浦东新区羽毛球馆</text>
      </view>
      <view class="sport-meta-item">
        <text class="sport-meta-icon">👥</text>
        <text>6/8人</text>
      </view>
    </view>
    <view class="flex items-center justify-between">
      <text class="text-lg font-bold text-primary">¥45</text>
      <button class="btn btn-primary btn-sm">立即参与</button>
    </view>
  </view>
</view>
```

### 不同运动类型卡片
```html
<!-- WXML -->
<view class="sport-cards">
  <!-- 篮球 -->
  <view class="activity-card sport-basketball">
    <view class="activity-card-image">
      <text class="sport-icon">🏀</text>
    </view>
    <view class="activity-card-content">
      <text class="sport-card-title">篮球友谊赛</text>
    </view>
  </view>
  
  <!-- 网球 -->
  <view class="activity-card sport-tennis">
    <view class="activity-card-image">
      <text class="sport-icon">🎾</text>
    </view>
    <view class="activity-card-content">
      <text class="sport-card-title">网球双打体验</text>
    </view>
  </view>
  
  <!-- 足球 -->
  <view class="activity-card sport-football">
    <view class="activity-card-image">
      <text class="sport-icon">⚽</text>
    </view>
    <view class="activity-card-content">
      <text class="sport-card-title">足球11人制</text>
    </view>
  </view>
</view>
```

## 🏷️ 标签组件示例

### 基础标签
```html
<!-- WXML -->
<view class="tag-examples">
  <view class="tag tag-primary">羽毛球</view>
  <view class="tag tag-success">已确认</view>
  <view class="tag tag-warning">待审核</view>
  <view class="tag tag-danger">已取消</view>
</view>
```

### 运动标签
```html
<!-- WXML -->
<view class="sport-tags">
  <view class="sport-tag sport-tag-hot">🔥 热门</view>
  <view class="sport-tag sport-tag-new">🆕 新活动</view>
  <view class="sport-tag sport-tag-limited">⚡ 仅剩2位</view>
</view>
```

## 📝 表单组件示例

### 基础表单
```html
<!-- WXML -->
<view class="form-example">
  <view class="form-group">
    <text class="form-label">活动标题</text>
    <input class="form-input" placeholder="请输入活动标题" />
  </view>
  
  <view class="form-group">
    <text class="form-label">活动描述</text>
    <textarea class="form-input form-textarea" placeholder="请描述活动详情"></textarea>
  </view>
  
  <view class="form-group">
    <text class="form-label">参与人数</text>
    <input class="form-input" type="number" placeholder="请输入人数" />
  </view>
  
  <button class="btn btn-primary w-full">发布活动</button>
</view>
```

### 表单验证状态
```html
<!-- WXML -->
<view class="form-validation">
  <!-- 正常状态 -->
  <view class="form-group">
    <text class="form-label">邮箱地址</text>
    <input class="form-input" placeholder="请输入邮箱" />
  </view>
  
  <!-- 错误状态 -->
  <view class="form-group">
    <text class="form-label">手机号码</text>
    <input class="form-input form-input-error" placeholder="请输入手机号" />
    <text class="text-xs text-danger">请输入正确的手机号码</text>
  </view>
</view>
```

## 👤 头像组件示例

### 不同尺寸头像
```html
<!-- WXML -->
<view class="avatar-examples">
  <image class="avatar avatar-xs" src="/images/avatar.jpg" />
  <image class="avatar avatar-sm" src="/images/avatar.jpg" />
  <image class="avatar avatar-md" src="/images/avatar.jpg" />
  <image class="avatar avatar-lg" src="/images/avatar.jpg" />
  <image class="avatar avatar-xl" src="/images/avatar.jpg" />
</view>
```

### 带边框头像
```html
<!-- WXML -->
<view class="avatar-with-ring">
  <image class="avatar avatar-lg avatar-ring" src="/images/avatar.jpg" />
  <image class="avatar avatar-lg avatar-ring-gradient" src="/images/avatar.jpg" />
</view>
```

## 🏆 运动统计示例

### 统计卡片
```html
<!-- WXML -->
<view class="sport-stats sport-badminton">
  <view class="sport-stat-item">
    <text class="sport-stat-number">12</text>
    <text class="sport-stat-label">参与活动</text>
  </view>
  <view class="sport-stat-item">
    <text class="sport-stat-number">8</text>
    <text class="sport-stat-label">组织活动</text>
  </view>
  <view class="sport-stat-item">
    <text class="sport-stat-number">4.8</text>
    <text class="sport-stat-label">平均评分</text>
  </view>
</view>
```

### 进度条
```html
<!-- WXML -->
<view class="progress-example sport-tennis">
  <view class="flex items-center justify-between mb-sm">
    <text class="text-sm font-medium">活动完成度</text>
    <text class="text-sm text-secondary">75%</text>
  </view>
  <view class="sport-progress">
    <view class="sport-progress-bar" style="width: 75%"></view>
  </view>
</view>
```

## 🎖️ 成就徽章示例

### 等级徽章
```html
<!-- WXML -->
<view class="level-badges">
  <view class="sport-level-badge sport-badminton">
    <text class="sport-level-icon">🏸</text>
    <text>羽毛球专家</text>
  </view>
  
  <view class="sport-level-badge sport-basketball">
    <text class="sport-level-icon">🏀</text>
    <text>篮球新手</text>
  </view>
</view>
```

### 成就展示
```html
<!-- WXML -->
<view class="sport-achievement sport-tennis">
  <view class="sport-achievement-icon">🎾</view>
  <view class="sport-achievement-content">
    <text class="sport-achievement-title">网球达人</text>
    <text class="sport-achievement-desc">参与10场网球活动</text>
  </view>
  <text class="sport-achievement-points">+50</text>
</view>
```

## 🔄 加载状态示例

### 加载动画
```html
<!-- WXML -->
<view class="loading-examples">
  <!-- 旋转加载 -->
  <view class="loading"></view>
  
  <!-- 点状加载 -->
  <view class="loading-dots">
    <view class="dot"></view>
    <view class="dot"></view>
    <view class="dot"></view>
  </view>
  
  <!-- 脉冲加载 -->
  <view class="loading-pulse">加载中...</view>
</view>
```

### 骨架屏
```html
<!-- WXML -->
<view class="skeleton-example">
  <view class="loading-skeleton" style="height: 40rpx; margin-bottom: 16rpx;"></view>
  <view class="loading-skeleton" style="height: 20rpx; width: 60%; margin-bottom: 16rpx;"></view>
  <view class="loading-skeleton" style="height: 20rpx; width: 80%;"></view>
</view>
```

## 🎭 动画效果示例

### 页面动画
```html
<!-- WXML -->
<view class="page-container page-enter">
  <text class="text-2xl font-bold">页面内容</text>
</view>
```

### 卡片动画
```html
<!-- WXML -->
<view class="card card-slide-up delay-100">
  <view class="card-body">
    <text>滑入动画卡片</text>
  </view>
</view>

<view class="card card-scale-in delay-200">
  <view class="card-body">
    <text>缩放动画卡片</text>
  </view>
</view>
```

### 按钮动画
```html
<!-- WXML -->
<button class="btn btn-primary btn-ripple" bindtap="onButtonTap">
  点击涟漪效果
</button>

<button class="btn btn-secondary float" bindtap="onFloatButtonTap">
  浮动效果按钮
</button>
```

## 📱 响应式布局示例

### 网格布局
```html
<!-- WXML -->
<view class="grid-layout">
  <view class="activity-card sport-badminton">卡片1</view>
  <view class="activity-card sport-basketball">卡片2</view>
  <view class="activity-card sport-tennis">卡片3</view>
  <view class="activity-card sport-football">卡片4</view>
</view>
```

```css
/* WXSS */
.grid-layout {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300rpx, 1fr));
  gap: var(--space-lg);
  padding: var(--space-lg);
}
```

### 弹性布局
```html
<!-- WXML -->
<view class="flex-layout">
  <view class="flex-item">项目1</view>
  <view class="flex-item">项目2</view>
  <view class="flex-item">项目3</view>
</view>
```

```css
/* WXSS */
.flex-layout {
  display: flex;
  gap: var(--space-md);
  padding: var(--space-lg);
}

.flex-item {
  flex: 1;
  padding: var(--space-md);
  background: var(--bg-card);
  border-radius: var(--radius-md);
  text-align: center;
}
```
